from flask import request, jsonify
from datetime import datetime
from config import ADMIN_PASSWORD, logger, get_sku_type, config

def register_bot_api_routes(app, db):
    """注册机器人API相关路由"""
    
    @app.route('/api/check-authorization', methods=['POST'])
    def check_authorization():
        try:
            data = request.json
            group_id = data.get('groupId', '').strip()
            
            if not group_id:
                return jsonify({'success': False, 'message': '群号不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            # 获取该群的所有有效套餐
            cursor.execute('''
                SELECT DISTINCT b.sku_id, b.expiration_time, 
                       MIN(b.bind_time) as first_bind_time,
                       GROUP_CONCAT(DISTINCT b.owner_username) as owners
                FROM bindings b
                WHERE b.group_number = ? 
                AND (b.expiration_time IS NULL OR datetime(b.expiration_time) > datetime('now'))
                GROUP BY b.sku_id, b.expiration_time
                ORDER BY b.sku_id
            ''', (group_id,))
            
            bindings = cursor.fetchall()
            
            if bindings and len(bindings) > 0:
                # 构建所有套餐信息
                tiers = []
                main_tier = None
                all_owners = set()
                
                for binding in bindings:
                    days_left = None
                    if binding['expiration_time']:
                        expiry_time = datetime.fromisoformat(binding['expiration_time'])
                        days_left = (expiry_time - datetime.now()).days
                    
                    owners_list = binding['owners'].split(',') if binding['owners'] else []
                    all_owners.update(owners_list)
                    
                    tier_info = {
                        'skuType': config.get_tier_name(binding['sku_id']),
                        'skuId': binding['sku_id'],
                        'expirationDate': binding['expiration_time'] if binding['expiration_time'] else '永久',
                        'daysLeft': days_left,
                        'owners': owners_list
                    }
                    
                    tiers.append(tier_info)
                    
                    # 选择最高级的套餐作为主要套餐
                    if not main_tier or binding['sku_id'] > main_tier['skuId']:
                        main_tier = tier_info
                
                # 获取主要所有者的详细信息（为了向后兼容）
                cursor.execute('''
                    SELECT username, email FROM users 
                    WHERE username = ?
                    LIMIT 1
                ''', (list(all_owners)[0] if all_owners else '',))
                owner_info = cursor.fetchone()
                
                return jsonify({
                    'success': True,
                    'authorized': True,
                    'skuType': main_tier['skuType'],  # 保持向后兼容
                    'skuId': main_tier['skuId'],      # 保持向后兼容
                    'expirationDate': main_tier['expirationDate'],  # 保持向后兼容
                    'daysLeft': main_tier['daysLeft'],  # 保持向后兼容
                    'allTiers': tiers,  # 新增：所有套餐信息
                    'owners': list(all_owners),  # 所有所有者
                    'owner': {
                        'username': owner_info['username'],
                        'email': owner_info['email']
                    } if owner_info else None
                })
            else:
                return jsonify({
                    'success': True,
                    'authorized': False,
                    'message': '该群聊未授权'
                })
                
        except Exception as e:
            logger.error(f'检查群聊授权状态时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/check-feature-blacklist', methods=['POST'])
    def check_feature_blacklist():
        """检查群组的功能黑名单"""
        try:
            data = request.json
            group_id = data.get('groupId', '').strip()
            request_identifier = data.get('requestIdentifier', '').strip()
            
            if not group_id or not request_identifier:
                return jsonify({'success': False, 'message': '参数不完整'}), 400
            
            cursor = db.conn.cursor()
            
            # 查询该群组启用的功能黑名单
            cursor.execute('''
                SELECT f.actual_features
                FROM group_feature_blacklist gb
                JOIN feature_blacklist_definitions f ON gb.feature_id = f.id
                WHERE gb.group_number = ? 
                AND f.request_identifiers LIKE '%' || ? || '%'
            ''', (group_id, request_identifier))
            
            result = cursor.fetchone()
            
            if result:
                # 该请求标识符对应的功能被禁用
                disabled_features = result['actual_features'].split(',')
                return jsonify({
                    'success': True,
                    'blacklisted': True,
                    'disabledFeatures': disabled_features
                })
            else:
                # 功能未被禁用
                return jsonify({
                    'success': True,
                    'blacklisted': False,
                    'disabledFeatures': []
                })
        except Exception as e:
            logger.error(f'检查功能黑名单时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/check-feature-whitelist', methods=['POST'])
    def check_feature_whitelist():
        """检查群组的功能白名单"""
        try:
            data = request.json
            group_id = data.get('groupId', '').strip()
            request_identifier = data.get('requestIdentifier', '').strip()
            
            if not group_id or not request_identifier:
                return jsonify({'success': False, 'message': '参数不完整'}), 400
            
            cursor = db.conn.cursor()
            
            # 首先查找匹配请求标识符的白名单定义
            cursor.execute('''
                SELECT id, actual_features, allowed_tiers
                FROM feature_whitelist_definitions
                WHERE request_identifiers LIKE '%' || ? || '%'
            ''', (request_identifier,))
            
            whitelist_definitions = cursor.fetchall()
            
            if not whitelist_definitions:
                # 没有找到匹配的白名单定义
                return jsonify({
                    'success': True,
                    'whitelisted': False,
                    'whitelistedFeatures': [],
                    'allowedTiers': []
                })
            
            # 获取群组当前的套餐信息
            cursor.execute('''
                SELECT sku_id FROM bindings 
                WHERE group_number = ? 
                AND (expiration_time IS NULL OR expiration_time > datetime('now'))
                ORDER BY bind_time DESC
                LIMIT 1
            ''', (group_id,))
            
            binding = cursor.fetchone()
            
            if not binding:
                # 群组没有有效的套餐激活
                return jsonify({
                    'success': True,
                    'whitelisted': False,
                    'whitelistedFeatures': [],
                    'allowedTiers': []
                })
            
            current_sku_id = binding['sku_id']
            
            # 检查当前套餐是否在任何白名单定义的允许套餐中
            whitelisted_features = []
            allowed_tiers = set()
            matched = False
            
            for definition in whitelist_definitions:
                allowed_sku_ids = definition['allowed_tiers'].split(',') if definition['allowed_tiers'] else []
                # 清理空白字符
                allowed_sku_ids = [sku.strip() for sku in allowed_sku_ids if sku.strip()]
                
                if current_sku_id in allowed_sku_ids:
                    matched = True
                    features = definition['actual_features'].split(',') if definition['actual_features'] else []
                    whitelisted_features.extend([f.strip() for f in features if f.strip()])
                    allowed_tiers.update(allowed_sku_ids)
            
            if matched:
                # 去重
                whitelisted_features = list(set(whitelisted_features))
                allowed_tiers = list(allowed_tiers)
                
                return jsonify({
                    'success': True,
                    'whitelisted': True,
                    'whitelistedFeatures': whitelisted_features,
                    'allowedTiers': allowed_tiers
                })
            else:
                # 当前套餐不在允许的套餐列表中
                return jsonify({
                    'success': True,
                    'whitelisted': False,
                    'whitelistedFeatures': [],
                    'allowedTiers': []
                })
                
        except Exception as e:
            logger.error(f'检查功能白名单时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/sync-groups', methods=['POST'])
    def sync_bot_groups():
        try:
            data = request.json
            bot_account = data.get('botAccount', '').strip()
            bot_name = data.get('botName', '').strip()
            groups = data.get('groups', [])
            
            if not bot_account:
                return jsonify({'success': False, 'message': '机器人账号不能为空'}), 400
            cursor = db.conn.cursor()
            cursor.execute('SELECT id FROM robots WHERE bot_account = ?', (bot_account,))
            robot = cursor.fetchone()
            if not robot:
                return jsonify({
                    'success': False, 
                    'message': '机器人未在系统中注册，请先在后台添加机器人配置'
                }), 404
            
            robot_id = robot['id']
            
            if bot_name:
                cursor.execute(
                    'UPDATE robots SET bot_name = ?, updated_time = ? WHERE id = ?',
                    (bot_name, datetime.now().isoformat(), robot_id)
                )
            
            db.conn.execute('BEGIN IMMEDIATE')
            
            try:
                cursor.execute('SELECT group_id FROM robot_groups WHERE robot_id = ?', (robot_id,))
                existing_groups = set(row['group_id'] for row in cursor.fetchall())
                
                new_group_ids = set(g['groupId'] for g in groups)
                
                groups_to_delete = existing_groups - new_group_ids
                if groups_to_delete:
                    placeholders = ','.join('?' * len(groups_to_delete))
                    cursor.execute(
                        f'DELETE FROM robot_groups WHERE robot_id = ? AND group_id IN ({placeholders})',
                        [robot_id] + list(groups_to_delete)
                    )
                
                for group in groups:
                    cursor.execute('''
                        INSERT OR REPLACE INTO robot_groups 
                        (robot_id, group_id, group_name, member_count, max_member_count, group_remark, last_update)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        robot_id,
                        group['groupId'],
                        group.get('groupName', '未知群名'),
                        group.get('memberCount', 0),
                        group.get('maxMemberCount', 0),
                        group.get('groupRemark', ''),
                        datetime.now().isoformat()
                    ))
                
                db.conn.commit()
                
                logger.info(f'机器人 {bot_account} 同步了 {len(groups)} 个群聊')
                
                return jsonify({
                    'success': True,
                    'message': f'成功同步 {len(groups)} 个群聊',
                    'data': {
                        'syncedCount': len(groups),
                        'deletedCount': len(groups_to_delete)
                    }
                })
                
            except Exception as e:
                db.conn.rollback()
                raise e
                
        except Exception as e:
            logger.error(f'同步群聊信息时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/check-authorizations', methods=['POST'])
    def check_group_authorizations():
        try:
            data = request.json
            group_ids = data.get('groupIds', [])
            
            if not group_ids:
                return jsonify({'success': False, 'message': '群号列表不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            placeholders = ','.join('?' * len(group_ids))
            cursor.execute(f'''
                SELECT b.group_number, b.sku_id, b.expiration_time, 
                       GROUP_CONCAT(DISTINCT b.owner_username) as owners
                FROM bindings b
                WHERE b.group_number IN ({placeholders})
                AND (b.expiration_time IS NULL OR datetime(b.expiration_time) > datetime('now'))
                GROUP BY b.group_number, b.sku_id, b.expiration_time
            ''', group_ids)
            
            # 整理数据
            group_data = {}
            for row in cursor.fetchall():
                group_id = row['group_number']
                if group_id not in group_data:
                    group_data[group_id] = {
                        'authorized': True,
                        'tiers': []
                    }
                
                group_data[group_id]['tiers'].append({
                    'skuType': config.get_tier_name(row['sku_id']),
                    'skuId': row['sku_id'],
                    'expirationDate': row['expiration_time'] if row['expiration_time'] else '永久',
                    'owners': row['owners'].split(',') if row['owners'] else []
                })
            
            # 构建结果
            results = {}
            for group_id in group_ids:
                if group_id in group_data:
                    data = group_data[group_id]
                    # 选择最高级套餐作为主要信息
                    main_tier = max(data['tiers'], key=lambda x: x['skuId'])
                    results[group_id] = {
                        'authorized': True,
                        'skuType': main_tier['skuType'],
                        'skuId': main_tier['skuId'],
                        'expirationDate': main_tier['expirationDate'],
                        'allTiers': data['tiers'],
                        'owner': main_tier['owners'][0] if main_tier['owners'] else None
                    }
                else:
                    results[group_id] = {'authorized': False}
            
            return jsonify({
                'success': True,
                'data': results
            })
            
        except Exception as e:
            logger.error(f'批量检查群聊授权时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/authorized-groups', methods=['GET'])
    def get_authorized_groups():
        try:
            sku_id = request.args.get('skuId')
            
            cursor = db.conn.cursor()
            
            query = '''
                SELECT DISTINCT b.group_number, b.sku_id, 
                       MAX(b.expiration_time) as expiration_time,
                       GROUP_CONCAT(DISTINCT b.owner_username) as owners
                FROM bindings b
                WHERE (b.expiration_time IS NULL OR datetime(b.expiration_time) > datetime('now'))
            '''
            
            params = []
            if sku_id:
                query += ' AND b.sku_id = ?'
                params.append(sku_id)
            
            query += ' GROUP BY b.group_number, b.sku_id'
            
            cursor.execute(query, params)
            
            groups = []
            for row in cursor.fetchall():
                owners_list = row['owners'].split(',') if row['owners'] else []
                groups.append({
                    'groupId': row['group_number'],
                    'skuId': row['sku_id'],
                    'skuType': config.get_tier_name(row['sku_id']),
                    'expirationDate': row['expiration_time'] if row['expiration_time'] else '永久',
                    'owners': owners_list,
                    'owner': owners_list[0] if owners_list else None  # 向后兼容
                })
            
            return jsonify({
                'success': True,
                'data': groups
            })
            
        except Exception as e:
            logger.error(f'获取授权群聊列表时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/bot-heartbeat', methods=['POST'])
    def bot_heartbeat():
        try:
            data = request.json
            bot_account = data.get('botAccount', '').strip()
            bot_name = data.get('botName', '').strip()
            api_url = data.get('apiUrl', '').strip()
            
            if not bot_account:
                return jsonify({'success': False, 'message': '机器人账号不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            cursor.execute('SELECT id FROM robots WHERE bot_account = ?', (bot_account,))
            robot = cursor.fetchone()
            
            if robot:
                cursor.execute(
                    'UPDATE robots SET updated_time = ? WHERE id = ?',
                    (datetime.now().isoformat(), robot['id'])
                )
            else:
                if bot_name and api_url:
                    cursor.execute('''
                        INSERT INTO robots (bot_account, bot_name, api_url, api_token, is_active)
                        VALUES (?, ?, ?, '', 1)
                    ''', (bot_account, bot_name, api_url))
                    logger.info(f'自动注册新机器人: {bot_account}')
            
            db.conn.commit()
            
            return jsonify({
                'success': True,
                'message': '心跳接收成功'
            })
            
        except Exception as e:
            logger.error(f'处理机器人心跳时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/expiring-groups', methods=['GET'])
    def get_expiring_groups():
        try:
            days = int(request.args.get('days', 7))
            
            cursor = db.conn.cursor()
            
            # 修改查询，包含所有套餐信息
            cursor.execute('''
                SELECT b.group_number, b.sku_id, b.expiration_time,
                       GROUP_CONCAT(DISTINCT b.owner_username) as owners,
                       (julianday(b.expiration_time) - julianday('now')) as days_left
                FROM bindings b
                WHERE b.expiration_time IS NOT NULL
                AND datetime(b.expiration_time) > datetime('now')
                AND (julianday(b.expiration_time) - julianday('now')) <= ?
                GROUP BY b.group_number, b.sku_id, b.expiration_time
                ORDER BY b.expiration_time ASC
            ''', (days,))
            
            expiring_groups = []
            for row in cursor.fetchall():
                owners_list = row['owners'].split(',') if row['owners'] else []
                
                # 获取第一个所有者的详细信息
                cursor.execute('SELECT username, email FROM users WHERE username = ? LIMIT 1', 
                             (owners_list[0] if owners_list else '',))
                owner_info = cursor.fetchone()
                
                expiring_groups.append({
                    'groupId': row['group_number'],
                    'skuType': config.get_tier_name(row['sku_id']),
                    'skuId': row['sku_id'],
                    'expirationDate': row['expiration_time'],
                    'daysLeft': int(row['days_left']),
                    'owners': owners_list,
                    'owner': {
                        'username': owner_info['username'],
                        'email': owner_info['email']
                    } if owner_info else None
                })
            
            return jsonify({
                'success': True,
                'data': expiring_groups
            })
            
        except Exception as e:
            logger.error(f'获取到期提醒时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/check-authorization-with-user', methods=['POST'])
    def check_authorization_with_user():
        data = request.json
        group_id = data.get('groupId')
        user_qq = data.get('userId')
        
        if not group_id or not user_qq:
            return jsonify({'authorized': False, 'reason': '缺少必要参数'})
        
        try:
            cursor = db.conn.cursor()
            
            cursor.execute('SELECT username FROM users WHERE qq = ?', (user_qq,))
            user = cursor.fetchone()
            
            if not user:
                return jsonify({'authorized': False, 'reason': '该QQ号未注册'})
            
            username = user['username']
            
            # 检查该用户是否为该群的任何套餐贡献过续费
            cursor.execute('''
                SELECT DISTINCT b.sku_id, MAX(b.expiration_time) as expiration_time
                FROM bindings b
                WHERE b.group_number = ? 
                AND b.owner_username = ? 
                AND (b.expiration_time IS NULL OR b.expiration_time > ?)
                GROUP BY b.sku_id
            ''', (group_id, username, datetime.now().isoformat()))
            
            bindings = cursor.fetchall()
            
            if not bindings:
                return jsonify({'authorized': False, 'reason': '您未激活该群聊的授权'})
            
            # 返回所有该用户贡献的套餐信息
            user_tiers = []
            main_tier = None
            
            for binding in bindings:
                days_left = None
                if binding['expiration_time']:
                    expiration = datetime.fromisoformat(binding['expiration_time'])
                    days_left = (expiration - datetime.now()).days
                    
                    if days_left < 0:
                        continue  # 跳过已过期的
                
                tier_info = {
                    'skuType': config.get_tier_name(binding['sku_id']),
                    'skuId': binding['sku_id'],
                    'expirationDate': binding['expiration_time'],
                    'daysLeft': days_left
                }
                
                user_tiers.append(tier_info)
                
                # 选择最高级的套餐作为主要套餐
                if not main_tier or binding['sku_id'] > main_tier['skuId']:
                    main_tier = tier_info
            
            if not user_tiers:
                return jsonify({'authorized': False, 'reason': '授权已过期'})
            
            return jsonify({
                'authorized': True,
                'skuType': main_tier['skuType'],
                'expirationDate': main_tier['expirationDate'],
                'daysLeft': main_tier['daysLeft'],
                'username': username,
                'userTiers': user_tiers  # 该用户贡献的所有套餐
            })
            
        except Exception as e:
            logger.error('检查授权时出错:', e)
            return jsonify({'authorized': False, 'reason': '检查授权时发生错误'}), 500
    
    @app.route('/api/check-user-by-qq', methods=['POST'])
    def check_user_by_qq():
        data = request.json
        qq = data.get('qq', '').strip()
        
        if not qq:
            return jsonify({'isRegistered': False, 'message': '缺少QQ号参数'})
        
        try:
            cursor = db.conn.cursor()
            cursor.execute('SELECT username FROM users WHERE qq = ?', (qq,))
            user = cursor.fetchone()
            
            if user:
                return jsonify({
                    'isRegistered': True,
                    'username': user['username']
                })
            else:
                return jsonify({
                    'isRegistered': False
                })
        except Exception as e:
            logger.error('检查用户QQ时出错:', e)
            return jsonify({'isRegistered': False, 'message': '检查失败'}), 500
    
    @app.route('/api/admin/bindings')
    def get_admin_bindings():
        admin_password = request.headers.get('X-Admin-Password')
        if admin_password != ADMIN_PASSWORD:
            return jsonify({'error': '未授权'}), 401
        
        try:
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT 
                    u.id,
                    u.username,
                    u.qq,
                    u.register_time as bind_time
                FROM users u
                ORDER BY u.id DESC
            ''')
            
            bindings = []
            for row in cursor.fetchall():
                bindings.append({
                    'id': row['id'],
                    'username': row['username'],
                    'qq': row['qq'],
                    'bindTime': row['bind_time']
                })
            
            return jsonify(bindings)
        except Exception as e:
            logger.error('获取绑定列表时出错:', e)
            return jsonify({'error': '获取绑定列表失败'}), 500
    
    @app.route('/api/bot/tiers', methods=['GET'])
    def get_bot_tiers():
        """获取档位信息供机器人使用"""
        try:
            # 只返回必要的信息
            tiers_info = []
            for tier in config.TIERS:
                tiers_info.append({
                    'skuId': tier['sku_id'],
                    'name': tier['display_name'],
                    'price': tier.get('price', 0),
                    'features': tier.get('features', [])
                })
            
            return jsonify({
                'success': True,
                'data': tiers_info
            })
        except Exception as e:
            logger.error(f'获取档位信息时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500

    # ==================== 按次付费功能机器人接口 ====================

    @app.route('/api/pay-per-use/redeem', methods=['POST'])
    def redeem_pay_per_use_code():
        """兑换按次付费兑换码"""
        try:
            data = request.json
            group_id = data.get('groupId', '').strip()
            code = data.get('code', '').strip()

            if not group_id or not code:
                return jsonify({'success': False, 'message': '群号和兑换码不能为空'}), 400

            cursor = db.conn.cursor()

            # 检查兑换码是否存在且有效
            cursor.execute('''
                SELECT ppc.*
                FROM pay_per_use_codes ppc
                WHERE ppc.code = ? AND ppc.is_used = 0
            ''', (code,))

            code_row = cursor.fetchone()
            if not code_row:
                return jsonify({'success': False, 'message': '兑换码无效或已被使用'}), 400

            # 检查兑换码是否过期
            if code_row['expires_at']:
                from datetime import datetime
                try:
                    expires_at = datetime.fromisoformat(code_row['expires_at'])
                    if datetime.now() > expires_at:
                        return jsonify({'success': False, 'message': '兑换码已过期'}), 400
                except Exception:
                    pass

            # 解析功能配置
            feature_config = {}
            if code_row['feature_config']:
                try:
                    import json
                    feature_config = json.loads(code_row['feature_config'])
                except:
                    return jsonify({'success': False, 'message': '兑换码配置错误'}), 500

            if not feature_config:
                return jsonify({'success': False, 'message': '兑换码配置错误'}), 500

            # 为每个功能创建或更新计费账户
            redeemed_features = []
            for feature_id, usage_count in feature_config.items():
                # 获取功能信息
                cursor.execute('SELECT id, feature_code, display_name FROM pay_per_use_features WHERE id = ?', (feature_id,))
                feature = cursor.fetchone()
                if not feature:
                    continue

                # 检查群聊计费账户是否存在
                cursor.execute('''
                    SELECT * FROM pay_per_use_billing
                    WHERE group_number = ? AND feature_id = ?
                ''', (group_id, feature_id))

                billing_account = cursor.fetchone()

                if billing_account:
                    # 更新现有账户
                    new_remaining = billing_account['remaining_count'] + usage_count
                    new_total = billing_account['total_purchased'] + usage_count
                    cursor.execute('''
                        UPDATE pay_per_use_billing
                        SET remaining_count = ?, total_purchased = ?, updated_time = CURRENT_TIMESTAMP
                        WHERE group_number = ? AND feature_id = ?
                    ''', (new_remaining, new_total, group_id, feature_id))
                else:
                    # 创建新账户
                    cursor.execute('''
                        INSERT INTO pay_per_use_billing (group_number, feature_id, remaining_count, total_purchased)
                        VALUES (?, ?, ?, ?)
                    ''', (group_id, feature_id, usage_count, usage_count))

                redeemed_features.append({
                    'feature_name': feature['display_name'],
                    'usage_count': usage_count
                })

            # 标记兑换码为已使用
            cursor.execute('''
                UPDATE pay_per_use_codes
                SET is_used = 1, used_by_username = ?, used_by_group = ?, used_time = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', ('system_redeem', group_id, code_row['id']))

            db.conn.commit()

            # 构建兑换结果消息
            feature_messages = '、'.join([f'{f["usage_count"]}次{f["feature_name"]}' for f in redeemed_features])
            logger.info(f'群聊{group_id}成功兑换兑换码{code}，获得{feature_messages}使用权')

            return jsonify({
                'success': True,
                'message': f'兑换成功！获得{feature_messages}使用权',
                'data': {
                    'redeemed_features': redeemed_features,
                    'total_count': sum(f['usage_count'] for f in redeemed_features)
                }
            })

        except Exception as e:
            logger.error(f'兑换按次付费兑换码时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/api/pay-per-use/use', methods=['POST'])
    def use_pay_per_use_feature():
        """使用按次付费功能（扣费）"""
        try:
            data = request.json
            group_id = data.get('groupId', '').strip()
            feature_code = data.get('featureCode', '').strip()
            request_note = data.get('requestNote', '').strip()

            if not group_id or not feature_code:
                return jsonify({'success': False, 'message': '群号和功能代码不能为空'}), 400

            cursor = db.conn.cursor()

            # 获取功能信息
            cursor.execute('SELECT * FROM pay_per_use_features WHERE feature_code = ?', (feature_code,))
            feature = cursor.fetchone()
            if not feature:
                return jsonify({'success': False, 'message': '功能不存在'}), 400

            # 检查群聊是否有激活的绑定
            cursor.execute('''
                SELECT COUNT(*) as count FROM bindings
                WHERE group_number = ? AND (expiration_time IS NULL OR datetime(expiration_time) > datetime('now'))
            ''', (group_id,))
            active_binding = cursor.fetchone()

            if not active_binding or active_binding['count'] == 0:
                return jsonify({'success': False, 'message': '该群聊未激活，无法使用增值功能'}), 403

            # 获取群聊的计费账户（选择任意一个有余额的账户）
            cursor.execute('''
                SELECT ppb.*
                FROM pay_per_use_billing ppb
                WHERE ppb.group_number = ? AND ppb.feature_id = ? AND ppb.remaining_count > 0
                LIMIT 1
            ''', (group_id, feature['id']))

            billing_account = cursor.fetchone()
            if not billing_account:
                return jsonify({'success': False, 'message': f'该群聊没有{feature["display_name"]}功能的可用次数'}), 400

            # 扣除一次使用权
            new_remaining = billing_account['remaining_count'] - 1
            cursor.execute('''
                UPDATE pay_per_use_billing
                SET remaining_count = ?, updated_time = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_remaining, billing_account['id']))

            # 记录使用日志
            cursor.execute('''
                INSERT INTO pay_per_use_logs (group_number, feature_id, usage_count, request_note)
                VALUES (?, ?, 1, ?)
            ''', (group_id, feature['id'], request_note))

            db.conn.commit()

            logger.info(f'群聊{group_id}使用{feature["display_name"]}功能，剩余{new_remaining}次')

            return jsonify({
                'success': True,
                'message': f'使用成功！剩余{new_remaining}次{feature["display_name"]}使用权',
                'data': {
                    'feature_name': feature['display_name'],
                    'remaining_count': new_remaining
                }
            })

        except Exception as e:
            logger.error(f'使用按次付费功能时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/api/pay-per-use/balance', methods=['POST'])
    def check_pay_per_use_balance():
        """检查群聊按次付费功能余额"""
        try:
            data = request.json
            group_id = data.get('groupId', '').strip()

            if not group_id:
                return jsonify({'success': False, 'message': '群号不能为空'}), 400

            cursor = db.conn.cursor()

            # 获取群聊的所有按次付费账户
            cursor.execute('''
                SELECT ppb.*, ppf.display_name as feature_name
                FROM pay_per_use_billing ppb
                LEFT JOIN pay_per_use_features ppf ON ppb.feature_id = ppf.id
                WHERE ppb.group_number = ?
                ORDER BY ppf.display_name
            ''', (group_id,))

            accounts = cursor.fetchall()

            balance_info = []
            for account in accounts:
                balance_info.append({
                    'feature_name': account['feature_name'],
                    'remaining_count': account['remaining_count'],
                    'total_purchased': account['total_purchased']
                })

            return jsonify({
                'success': True,
                'data': balance_info
            })

        except Exception as e:
            logger.error(f'检查按次付费功能余额时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/api/pay-per-use/<feature_code>', methods=['POST'])
    def redeem_feature_pay_per_use_code(feature_code):
        """兑换特定功能的按次付费兑换码"""
        try:
            data = request.json
            group_id = data.get('groupId', '').strip()
            code = data.get('code', '').strip()

            if not group_id or not code:
                return jsonify({'success': False, 'message': '群号和兑换码不能为空'}), 400

            cursor = db.conn.cursor()

            # 获取功能信息
            cursor.execute('SELECT * FROM pay_per_use_features WHERE feature_code = ? AND is_active = 1', (feature_code,))
            feature = cursor.fetchone()
            if not feature:
                return jsonify({'success': False, 'message': '功能不存在或已禁用'}), 400

            # 检查兑换码是否存在且有效
            cursor.execute('''
                SELECT ppc.*
                FROM pay_per_use_codes ppc
                WHERE ppc.code = ? AND ppc.is_used = 0
            ''', (code,))

            code_row = cursor.fetchone()
            if not code_row:
                return jsonify({'success': False, 'message': '兑换码无效或已被使用'}), 400

            # 检查兑换码是否过期
            if code_row['expires_at']:
                from datetime import datetime
                try:
                    expires_at = datetime.fromisoformat(code_row['expires_at'])
                    if datetime.now() > expires_at:
                        return jsonify({'success': False, 'message': '兑换码已过期'}), 400
                except Exception:
                    pass

            # 解析功能配置
            feature_config = {}
            if code_row['feature_config']:
                try:
                    import json
                    feature_config = json.loads(code_row['feature_config'])
                except:
                    return jsonify({'success': False, 'message': '兑换码配置错误'}), 500

            if not feature_config or str(feature['id']) not in feature_config:
                return jsonify({'success': False, 'message': '兑换码不包含此功能的使用权'}), 400

            usage_count = feature_config[str(feature['id'])]

            # 检查群聊是否有激活的绑定
            cursor.execute('''
                SELECT COUNT(*) as count FROM bindings
                WHERE group_number = ? AND (expiration_time IS NULL OR datetime(expiration_time) > datetime('now'))
            ''', (group_id,))
            active_binding = cursor.fetchone()

            if not active_binding or active_binding['count'] == 0:
                return jsonify({'success': False, 'message': '该群聊未激活，无法兑换增值功能'}), 403

            # 检查群聊计费账户是否存在
            cursor.execute('''
                SELECT * FROM pay_per_use_billing
                WHERE group_number = ? AND feature_id = ?
            ''', (group_id, feature['id']))

            billing_account = cursor.fetchone()

            if billing_account:
                # 更新现有账户
                new_remaining = billing_account['remaining_count'] + usage_count
                new_total = billing_account['total_purchased'] + usage_count
                cursor.execute('''
                    UPDATE pay_per_use_billing
                    SET remaining_count = ?, total_purchased = ?, updated_time = CURRENT_TIMESTAMP
                    WHERE group_number = ? AND feature_id = ?
                ''', (new_remaining, new_total, group_id, feature['id']))
            else:
                # 创建新账户
                cursor.execute('''
                    INSERT INTO pay_per_use_billing (group_number, feature_id, remaining_count, total_purchased)
                    VALUES (?, ?, ?, ?)
                ''', (group_id, feature['id'], usage_count, usage_count))

            # 标记兑换码为已使用
            cursor.execute('''
                UPDATE pay_per_use_codes
                SET is_used = 1, used_by_username = ?, used_by_group = ?, used_time = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', ('system_redeem', group_id, code_row['id']))

            db.conn.commit()

            logger.info(f'群聊{group_id}兑换{feature["display_name"]}兑换码{code}，获得{usage_count}次使用权')

            return jsonify({
                'success': True,
                'message': f'兑换成功！获得{usage_count}次{feature["display_name"]}使用权',
                'data': {
                    'feature_name': feature['display_name'],
                    'usage_count': usage_count,
                    'remaining_count': new_remaining if billing_account else usage_count
                }
            })

        except Exception as e:
            logger.error(f'兑换功能兑换码时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/api/pay-per-use/features', methods=['GET'])
    def get_pay_per_use_features():
        """获取可用的按次付费功能列表"""
        try:
            cursor = db.conn.cursor()
            cursor.execute('SELECT * FROM pay_per_use_features WHERE is_active = 1 ORDER BY display_name')
            features = cursor.fetchall()

            return jsonify({
                'success': True,
                'data': [dict(row) for row in features]
            })

        except Exception as e:
            logger.error(f'获取按次付费功能列表时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    # ==================== 新增简化API ====================
    
    @app.route('/api/simple/feature-blacklist/<group_id>', methods=['GET'])
    def get_simple_feature_blacklist(group_id):
        """新增功能黑名单API - 只需要提供群组ID，返回所有用户当前希望禁用的功能列表"""
        try:
            if not group_id.strip():
                return jsonify({'success': False, 'message': '群组ID不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            # 查询该群组启用的所有功能黑名单
            cursor.execute('''
                SELECT DISTINCT f.display_name, f.actual_features
                FROM group_feature_blacklist gb
                JOIN feature_blacklist_definitions f ON gb.feature_id = f.id
                WHERE gb.group_number = ?
                ORDER BY f.display_name
            ''', (group_id,))
            
            blacklist_rows = cursor.fetchall()
            
            # 整理返回的禁用功能列表
            disabled_features = []
            feature_names = []
            
            for row in blacklist_rows:
                feature_names.append(row['display_name'])
                if row['actual_features']:
                    features = row['actual_features'].split(',')
                    disabled_features.extend([f.strip() for f in features if f.strip()])
            
            # 去重
            disabled_features = list(set(disabled_features))
            
            return jsonify({
                'success': True,
                'group_id': group_id,
                'disabled_feature_names': feature_names,  # 用户禁用的功能名称
                'disabled_features': disabled_features,   # 实际禁用的功能列表
                'count': len(disabled_features)
            })
            
        except Exception as e:
            logger.error(f'获取群组功能黑名单时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/simple/feature-whitelist/<group_id>', methods=['GET'])
    def get_simple_feature_whitelist(group_id):
        """新增功能白名单API - 只需要提供群组ID，返回群组所属套餐的所有白名单列表"""
        try:
            if not group_id.strip():
                return jsonify({'success': False, 'message': '群组ID不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            # 获取群组当前的所有有效套餐
            cursor.execute('''
                SELECT DISTINCT sku_id FROM bindings 
                WHERE group_number = ? 
                AND (expiration_time IS NULL OR datetime(expiration_time) > datetime('now'))
            ''', (group_id,))
            
            bindings = cursor.fetchall()
            
            if not bindings:
                return jsonify({
                    'success': True,
                    'group_id': group_id,
                    'authorized': False,
                    'message': '该群组没有有效的套餐激活',
                    'whitelisted_features': [],
                    'allowed_tiers': []
                })
            
            # 获取所有套餐ID
            sku_ids = [binding['sku_id'] for binding in bindings]
            
            # 查询所有白名单定义，检查当前套餐是否在允许列表中
            cursor.execute('''
                SELECT DISTINCT display_name, actual_features, allowed_tiers
                FROM feature_whitelist_definitions
                ORDER BY display_name
            ''')
            
            whitelist_definitions = cursor.fetchall()
            
            whitelisted_features = []
            feature_names = []
            allowed_tiers = set()
            
            for definition in whitelist_definitions:
                if definition['allowed_tiers']:
                    allowed_sku_ids = [sku.strip() for sku in definition['allowed_tiers'].split(',') if sku.strip()]
                    allowed_tiers.update(allowed_sku_ids)
                    
                    # 检查当前群组的任何套餐是否在允许列表中
                    if any(sku_id in allowed_sku_ids for sku_id in sku_ids):
                        feature_names.append(definition['display_name'])
                        if definition['actual_features']:
                            features = definition['actual_features'].split(',')
                            whitelisted_features.extend([f.strip() for f in features if f.strip()])
            
            # 去重
            whitelisted_features = list(set(whitelisted_features))
            
            return jsonify({
                'success': True,
                'group_id': group_id,
                'authorized': True,
                'current_sku_ids': sku_ids,  # 当前群组的套餐ID列表
                'whitelisted_feature_names': feature_names,  # 白名单功能名称
                'whitelisted_features': whitelisted_features,  # 实际白名单功能列表
                'allowed_tiers': list(allowed_tiers),  # 所有相关的允许套餐
                'count': len(whitelisted_features)
            })
            
        except Exception as e:
            logger.error(f'获取群组功能白名单时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/simple/authorization/<group_id>', methods=['GET'])
    def get_simple_authorization(group_id):
        """新增授权检查API - 只需要提供群组ID，返回群组是否被授权"""
        try:
            if not group_id.strip():
                return jsonify({'success': False, 'message': '群组ID不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            # 获取该群的所有有效套餐
            cursor.execute('''
                SELECT COUNT(*) as count,
                       GROUP_CONCAT(DISTINCT sku_id) as sku_ids,
                       MIN(expiration_time) as earliest_expiration,
                       MAX(expiration_time) as latest_expiration
                FROM bindings 
                WHERE group_number = ? 
                AND (expiration_time IS NULL OR datetime(expiration_time) > datetime('now'))
            ''', (group_id,))
            
            result = cursor.fetchone()
            
            if result and result['count'] > 0:
                sku_ids = result['sku_ids'].split(',') if result['sku_ids'] else []
                
                # 计算最早过期时间的剩余天数
                earliest_days_left = None
                if result['earliest_expiration']:
                    from datetime import datetime
                    try:
                        earliest_expiry = datetime.fromisoformat(result['earliest_expiration'])
                        earliest_days_left = (earliest_expiry - datetime.now()).days
                    except:
                        pass
                
                return jsonify({
                    'success': True,
                    'group_id': group_id,
                    'authorized': True,
                    'sku_ids': sku_ids,
                    'tier_count': len(sku_ids),
                    'earliest_expiration': result['earliest_expiration'],
                    'latest_expiration': result['latest_expiration'],
                    'earliest_days_left': earliest_days_left
                })
            else:
                return jsonify({
                    'success': True,
                    'group_id': group_id,
                    'authorized': False,
                    'message': '该群组未授权'
                })
                
        except Exception as e:
            logger.error(f'检查群组授权时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/simple/pay-per-use-balance/<group_id>', methods=['GET'])
    def get_simple_pay_per_use_balance(group_id):
        """新增按次付费API - 只需要提供群组ID，返回该群组增值功能余额"""
        try:
            if not group_id.strip():
                return jsonify({'success': False, 'message': '群组ID不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            # 获取群组的所有按次付费账户余额
            cursor.execute('''
                SELECT ppb.remaining_count, ppb.total_purchased,
                       ppf.feature_code, ppf.display_name as feature_name
                FROM pay_per_use_billing ppb
                LEFT JOIN pay_per_use_features ppf ON ppb.feature_id = ppf.id
                WHERE ppb.group_number = ?
                ORDER BY ppf.display_name
            ''', (group_id,))
            
            accounts = cursor.fetchall()
            
            balance_info = []
            total_remaining = 0
            total_purchased = 0
            
            for account in accounts:
                feature_balance = {
                    'feature_code': account['feature_code'],
                    'feature_name': account['feature_name'],
                    'remaining_count': account['remaining_count'],
                    'total_purchased': account['total_purchased']
                }
                balance_info.append(feature_balance)
                total_remaining += account['remaining_count'] or 0
                total_purchased += account['total_purchased'] or 0
            
            return jsonify({
                'success': True,
                'group_id': group_id,
                'balance_details': balance_info,
                'summary': {
                    'total_features': len(balance_info),
                    'total_remaining_count': total_remaining,
                    'total_purchased_count': total_purchased
                }
            })
            
        except Exception as e:
            logger.error(f'获取群组按次付费余额时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    # ==================== 新增API：群组授权与BOT信息检查 ====================
    
    @app.route('/api/group-bots-info/<group_id>', methods=['GET'])
    def get_group_bots_info(group_id):
        """检查群组授权状态并返回群组所属BOT账号信息，避免群内存在多个bot"""
        try:
            if not group_id.strip():
                return jsonify({'success': False, 'message': '群组ID不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            # 首先检查群组是否有有效的授权
            cursor.execute('''
                SELECT DISTINCT b.sku_id, b.expiration_time, b.owner_username
                FROM bindings b
                WHERE b.group_number = ? 
                AND (b.expiration_time IS NULL OR datetime(b.expiration_time) > datetime('now'))
                ORDER BY b.sku_id DESC
            ''', (group_id,))
            
            authorization_records = cursor.fetchall()
            
            if not authorization_records:
                return jsonify({
                    'success': True,
                    'authorized': False,
                    'message': '该群组未授权',
                    'group_id': group_id,
                    'bots': []
                })
            
            # 群组已授权，查找该群组所在的所有机器人
            cursor.execute('''
                SELECT DISTINCT r.id, r.bot_account, r.bot_name, r.api_url, 
                       r.is_active, r.created_time, r.updated_time,
                       rg.group_name, rg.member_count, rg.max_member_count, 
                       rg.group_remark, rg.last_update
                FROM robot_groups rg
                JOIN robots r ON rg.robot_id = r.id
                WHERE rg.group_id = ?
                ORDER BY r.bot_account
            ''', (group_id,))
            
            bot_records = cursor.fetchall()
            
            # 构建授权信息
            tiers = []
            all_owners = set()
            
            for record in authorization_records:
                days_left = None
                if record['expiration_time']:
                    from datetime import datetime
                    try:
                        expiry_time = datetime.fromisoformat(record['expiration_time'])
                        days_left = (expiry_time - datetime.now()).days
                    except:
                        pass
                
                all_owners.add(record['owner_username'])
                
                tier_info = {
                    'sku_type': config.get_tier_name(record['sku_id']),
                    'sku_id': record['sku_id'],
                    'expiration_date': record['expiration_time'] if record['expiration_time'] else '永久',
                    'days_left': days_left,
                    'owner': record['owner_username']
                }
                tiers.append(tier_info)
            
            # 构建机器人信息
            bots_info = []
            active_bots_count = 0
            
            for bot_record in bot_records:
                bot_info = {
                    'bot_id': bot_record['id'],
                    'bot_account': bot_record['bot_account'],
                    'bot_name': bot_record['bot_name'],
                    'api_url': bot_record['api_url'],
                    'is_active': bool(bot_record['is_active']),
                    'created_time': bot_record['created_time'],
                    'updated_time': bot_record['updated_time'],
                    'group_info': {
                        'group_name': bot_record['group_name'],
                        'member_count': bot_record['member_count'],
                        'max_member_count': bot_record['max_member_count'],
                        'group_remark': bot_record['group_remark'],
                        'last_update': bot_record['last_update']
                    }
                }
                bots_info.append(bot_info)
                
                if bot_record['is_active']:
                    active_bots_count += 1
            
            # 警告信息（如果有多个活跃机器人）
            warnings = []
            if active_bots_count > 1:
                warnings.append(f'检测到该群组中有{active_bots_count}个活跃的机器人，可能存在冲突')
            
            # 选择主要套餐信息（最高级的套餐）
            main_tier = max(tiers, key=lambda x: x['sku_id']) if tiers else None
            
            return jsonify({
                'success': True,
                'authorized': True,
                'group_id': group_id,
                'authorization_info': {
                    'main_tier': main_tier,
                    'all_tiers': tiers,
                    'owners': list(all_owners),
                    'tier_count': len(tiers)
                },
                'bots_info': {
                    'total_bots': len(bots_info),
                    'active_bots': active_bots_count,
                    'bots': bots_info
                },
                'warnings': warnings,
                'recommendations': {
                    'single_bot_recommended': True,
                    'message': '建议每个群组只保留一个活跃的机器人以避免冲突' if active_bots_count > 1 else '群组机器人配置正常'
                }
            })
            
        except Exception as e:
            logger.error(f'获取群组机器人信息时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @app.route('/api/group-authorization-summary/<group_id>', methods=['GET'])
    def get_group_authorization_summary(group_id):
        """简化版API：只返回群组授权状态和主要BOT信息，用于快速检查"""
        try:
            if not group_id.strip():
                return jsonify({'success': False, 'message': '群组ID不能为空'}), 400
            
            cursor = db.conn.cursor()
            
            # 检查群组授权状态
            cursor.execute('''
                SELECT COUNT(*) as auth_count,
                       GROUP_CONCAT(DISTINCT sku_id) as sku_ids,
                       GROUP_CONCAT(DISTINCT owner_username) as owners
                FROM bindings 
                WHERE group_number = ? 
                AND (expiration_time IS NULL OR datetime(expiration_time) > datetime('now'))
            ''', (group_id,))
            
            auth_result = cursor.fetchone()
            
            if not auth_result or auth_result['auth_count'] == 0:
                return jsonify({
                    'success': True,
                    'authorized': False,
                    'group_id': group_id,
                    'message': '该群组未授权'
                })
            
            # 查找该群组的主要机器人（活跃的）
            cursor.execute('''
                SELECT r.bot_account, r.bot_name, r.is_active,
                       COUNT(*) OVER() as total_bots,
                       SUM(CASE WHEN r.is_active = 1 THEN 1 ELSE 0 END) OVER() as active_bots
                FROM robot_groups rg
                JOIN robots r ON rg.robot_id = r.id
                WHERE rg.group_id = ?
                ORDER BY r.is_active DESC, r.updated_time DESC
                LIMIT 1
            ''', (group_id,))
            
            bot_result = cursor.fetchone()
            
            # 构建简化响应
            response = {
                'success': True,
                'authorized': True,
                'group_id': group_id,
                'authorization': {
                    'sku_ids': auth_result['sku_ids'].split(',') if auth_result['sku_ids'] else [],
                    'owners': auth_result['owners'].split(',') if auth_result['owners'] else []
                }
            }
            
            if bot_result:
                response['primary_bot'] = {
                    'bot_account': bot_result['bot_account'],
                    'bot_name': bot_result['bot_name'],
                    'is_active': bool(bot_result['is_active'])
                }
                response['bot_status'] = {
                    'total_bots': bot_result['total_bots'],
                    'active_bots': bot_result['active_bots'],
                    'has_conflict': bot_result['active_bots'] > 1
                }
            else:
                response['primary_bot'] = None
                response['bot_status'] = {
                    'total_bots': 0,
                    'active_bots': 0,
                    'has_conflict': False
                }
                response['warnings'] = ['该群组已授权但未发现任何机器人']
            
            return jsonify(response)
            
        except Exception as e:
            logger.error(f'获取群组授权摘要时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500