// app.js - 用户前端JavaScript文件
console.log('app.js 开始加载');

// 全局变量
let isLoggedIn = false;
let token = null;
let currentUser = null;

// DOM元素（延迟获取）
let initialView, bindSection, loginSection, registerSection, userPanel;
let showBindButton, showLoginButton, showRegisterButton, showPayPerUseButton;

// 其他DOM元素（延迟获取）
let switchToLoginLink, switchToRegisterLink;
let userPanelBindButton, logoutButton;

const userWelcome = document.getElementById('userWelcome');
const userDisplayName = document.getElementById('userDisplayName');
const profileUsername = document.getElementById('profileUsername');
const profileEmail = document.getElementById('profileEmail');
const profileRegisterTime = document.getElementById('profileRegisterTime');

const userOrdersList = document.getElementById('userOrdersList');
const noOrdersMessage = document.getElementById('noOrdersMessage');
const ordersLoading = document.getElementById('ordersLoading');

const bindForm = document.getElementById('bindForm');
const bindButton = document.getElementById('bindButton');
const bindOrderNumberInput = document.getElementById('bindOrderNumber');
const bindGroupNumberInput = document.getElementById('bindGroupNumber');
const backFromBindButton = document.getElementById('backFromBind');

const loginForm = document.getElementById('loginForm');
const loginButton = document.getElementById('loginButton');
const backFromLoginButton = document.getElementById('backFromLogin');

const registerForm = document.getElementById('registerForm');
const registerButton = document.getElementById('registerButton');
const backFromRegisterButton = document.getElementById('backFromRegister');

// 模态框相关变量（延迟获取）
let modal, modalTitle, modalBody, modalError, closeModalButton, modalOkButton;

const profileQQ = document.getElementById('profileQQ');
// 新增：用户信息模块与邮件提醒开关
let publicSettings = { ticketsEnabled: true, featureBlacklistEnabled: true, registerEmailVerificationEnabled: false };
const userInfoContainer = document.createElement('div');
userInfoContainer.className = 'form-section';
userInfoContainer.id = 'userInfoSection';
userInfoContainer.innerHTML = `
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-semibold text-slate-700">账户信息</h2>
        <button id="backToMainFromUserInfo" class="text-blue-600 hover:text-blue-800 font-medium">返回主菜单</button>
    </div>
    <div class="bg-gray-50 p-4 rounded-lg border space-y-2 text-sm">
        <p><span class="text-gray-600">用户名:</span> <span id="uiUsername" class="font-medium">-</span></p>
        <p><span class="text-gray-600">邮箱:</span> <span id="uiEmail" class="font-medium">-</span></p>
        <p><span class="text-gray-600">QQ:</span> <span id="uiQQ" class="font-medium">-</span></p>
        <p><span class="text-gray-600">注册时间:</span> <span id="uiRegisterTime" class="font-medium">-</span></p>
    </div>
    <div class="mt-4 bg-gray-50 p-4 rounded-lg border">
        <label class="flex items-center space-x-2">
            <input id="toggleEmailNotify" type="checkbox" class="h-4 w-4">
            <span>开启邮件提醒</span>
        </label>
        <p class="text-xs text-gray-500 mt-1">用于重要通知与工单消息提醒。</p>
    </div>
`;
// 移除重复的DOMContentLoaded事件监听器，这个功能已经合并到下面的监听器中

// 工单相关变量
let currentTicketId = null;
let ticketTypes = [];

// 工具函数
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return unsafe;
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

function toggleButtonLoading(button, isLoading) {
    const textSpan = button.querySelector('.button-text');
    const loaderSpan = button.querySelector('.loader');
    if (isLoading) {
        button.disabled = true;
        textSpan.classList.add('hidden');
        loaderSpan.classList.remove('hidden');
    } else {
        button.disabled = false;
        textSpan.classList.remove('hidden');
        loaderSpan.classList.add('hidden');
    }
}

function showSection(sectionId) {
    console.log('尝试显示区域:', sectionId);
    // 统一隐藏所有区域，避免叠加显示
    if (typeof hideAllSections === 'function') {
        hideAllSections();
    }
    const section = document.getElementById(sectionId);
    if (section) {
        console.log('找到区域元素:', sectionId, '添加active类');
        section.classList.remove('hidden');
        section.classList.add('active');
        console.log('区域当前类:', section.className);
    } else {
        console.error('未找到区域元素:', sectionId);
    }
    // 清空表单
    if (bindOrderNumberInput) bindOrderNumberInput.value = '';
    if (bindGroupNumberInput) bindGroupNumberInput.value = '';

    if (document.getElementById('loginUsername')) document.getElementById('loginUsername').value = '';
    if (document.getElementById('loginPassword')) document.getElementById('loginPassword').value = '';
    if (document.getElementById('registerUsername')) document.getElementById('registerUsername').value = '';
    if (document.getElementById('registerEmail')) document.getElementById('registerEmail').value = '';
    if (document.getElementById('registerPassword')) document.getElementById('registerPassword').value = '';
    if (document.getElementById('confirmPassword')) document.getElementById('confirmPassword').value = '';

    modalError.classList.add('hidden');
    modalError.textContent = '';
}

function showModal(title, contentHtml, errorMessage = '') {
    console.log('showModal 被调用:', title);
    if (modalTitle) modalTitle.textContent = title;
    if (modalBody) modalBody.innerHTML = contentHtml;
    if (modalError) {
        if (errorMessage) {
            modalError.textContent = errorMessage;
            modalError.classList.remove('hidden');
        } else {
            modalError.classList.add('hidden');
            modalError.textContent = '';
        }
    }
    if (modal) {
        modal.style.display = 'flex'; // 强制显示
        modal.classList.remove('hidden');
        modal.classList.add('opacity-100');
        console.log('模态框已显示');
    }
}

function hideModal() {
    console.log('hideModal 被调用');
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('opacity-100');
        modal.style.display = 'none'; // 强制隐藏
        console.log('模态框已隐藏');
    }
    if (modalBody) {
        modalBody.innerHTML = '';
    }
    if (modalError) {
        modalError.textContent = '';
        modalError.classList.add('hidden');
    }
}

function checkLoginStatus() {
    const user = JSON.parse(localStorage.getItem('user'));
    if (user && user.token) {
        // 用户已登录
        if (userWelcome) userWelcome.textContent = `欢迎，${user.username}`;
        if (userDisplayName) userDisplayName.textContent = user.username;
        profileUsername.textContent = user.username;
        profileEmail.textContent = user.email;
        profileQQ.textContent = user.qq || '未绑定';  // 显示QQ号
        profileRegisterTime.textContent = user.registerTime || '未知';
        return true;
    }
    return false;
}

// 获取用户激活记录列表
async function fetchUserOrders() {
    try {
        ordersLoading.style.display = 'inline-block';
        noOrdersMessage.textContent = '加载中...';

        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }

        const response = await fetch('/user/bindings', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${user.token}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || '获取激活记录失败');
        }

        if (!result.success) {
            throw new Error(result.message || '获取激活记录失败');
        }

        return result.data || [];

    } catch (error) {
        console.error('获取用户激活记录失败:', error);
        throw error;
    } finally {
        ordersLoading.style.display = 'none';
    }
}

// 渲染用户激活记录列表
async function renderUserOrders() {
    try {
        const orders = await fetchUserOrders();

        // 清空现有内容
        userOrdersList.innerHTML = '';

        if (!orders.length) {
            noOrdersMessage.textContent = '您还没有绑定任何激活码';
            userOrdersList.appendChild(noOrdersMessage);
            return;
        }

        // 隐藏"无激活记录"消息
        noOrdersMessage.style.display = 'none';

        // 添加激活记录卡片（依赖 publicSettings）
        orders.forEach(order => {
            const orderCard = document.createElement('div');
            orderCard.className = 'bg-white p-4 rounded-lg shadow border border-gray-200 order-card';

            // 设置过期激活记录的样式
            const isExpired = new Date(order.expirationDate) < new Date() && !order.expirationDate.includes('永久');
            if (isExpired) {
                orderCard.classList.add('opacity-60');
            }

            // 续费标记
            const renewalBadge = order.renewalCount > 0 ?
                `<span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded ml-2">已续费 ${order.renewalCount} 次</span>` : '';

            // 激活码显示处理
            let activationDisplay = '';
            if (order.activationCodes && order.activationCodes.length > 1) {
                activationDisplay = `
                    <p class="text-sm text-gray-500">
                        使用的激活码: 
                        <span class="font-medium cursor-pointer text-blue-600 hover:text-blue-800" 
                              onclick="showActivationCodes(${JSON.stringify(order.activationCodes).replace(/"/g, '&quot;')})">
                            查看全部 ${order.activationCodes.length} 个
                        </span>
                    </p>
                `;
            } else {
                activationDisplay = `<p class="text-sm text-gray-500">激活码: ${escapeHtml(order.orderNumber)}</p>`;
            }

            orderCard.innerHTML = `
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-medium text-gray-900">
                            ${escapeHtml(order.skuType)}
                            ${renewalBadge}
                        </h4>
                        ${activationDisplay}
                    </div>
                    ${isExpired ? '<span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">已过期</span>' : ''}
                </div>
                <div class="mt-3 pt-3 border-t border-gray-100">
                    <p class="text-sm"><span class="text-gray-500">绑定群号:</span> <span class="font-medium">${escapeHtml(order.groupNumber)}</span></p>
                    <p class="text-sm"><span class="text-gray-500">有效期至:</span> <span class="font-medium">${escapeHtml(order.expirationDate)}</span></p>
                    <p class="text-xs text-gray-400 mt-1">首次绑定时间: ${new Date(order.bindTime).toLocaleString()}</p>
                </div>
                ${(!isExpired && publicSettings.featureBlacklistEnabled) ? `
                    <div class="mt-3 pt-3 border-t border-gray-100">
                        <button onclick="manageFeatureBlacklist('${escapeHtml(order.groupNumber)}')" 
                                class="w-full text-center bg-gray-600 hover:bg-gray-700 text-white text-sm py-2 px-3 rounded transition-colors">
                            管理功能黑名单
                        </button>
                    </div>
                ` : ''}
            `;

            userOrdersList.appendChild(orderCard);
        });

    } catch (error) {
        noOrdersMessage.textContent = `获取激活记录失败: ${error.message}`;
        console.error('渲染用户激活记录失败:', error);
    }
}

// 新增显示所有激活码的函数
function showActivationCodes(codes) {
    const codesList = codes.map(code => `<li class="py-1">• ${escapeHtml(code)}</li>`).join('');
    showModal('使用过的激活码', `
        <div class="max-h-60 overflow-y-auto">
            <p class="text-sm text-gray-600 mb-2">该群组使用过以下激活码：</p>
            <ul class="text-sm font-mono bg-gray-50 p-3 rounded">
                ${codesList}
            </ul>
        </div>
    `);
}

// 管理功能黑名单
async function manageFeatureBlacklist(groupNumber) {
    const modal = document.getElementById('featureBlacklistModal');
    const featureList = document.getElementById('featureList');
    const loading = document.getElementById('featureListLoading');
    const groupNumberSpan = document.getElementById('blacklistGroupNumber');

    // 显示模态框
    modal.classList.remove('hidden');
    groupNumberSpan.textContent = groupNumber;
    featureList.classList.add('hidden');
    loading.classList.remove('hidden');

    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录');
        }

        // 获取该群组的功能黑名单列表
        const response = await fetch(`/user/group/${groupNumber}/blacklist`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${user.token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取功能列表失败');
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.message || '获取功能列表失败');
        }

        // 渲染功能列表
        renderFeatureList(result.data, groupNumber);

    } catch (error) {
        console.error('获取功能黑名单失败:', error);
        alert('获取功能列表失败: ' + error.message);
        modal.classList.add('hidden');
    } finally {
        loading.classList.add('hidden');
        featureList.classList.remove('hidden');
    }
}

// 渲染功能列表
function renderFeatureList(features, groupNumber) {
    const featureList = document.getElementById('featureList');
    featureList.innerHTML = '';

    if (features.length === 0) {
        featureList.innerHTML = '<p class="text-center text-gray-500">当前套餐暂无可管理的功能</p>';
        return;
    }

    features.forEach(feature => {
        const featureCard = document.createElement('div');
        featureCard.className = 'bg-gray-50 p-4 rounded-lg border border-gray-200';
        featureCard.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">${escapeHtml(feature.displayName)}</h4>
                    <p class="text-sm text-gray-600 mt-1">包含功能：${feature.actualFeatures.join(', ')}</p>
                </div>
                <div class="ml-4">
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" 
                               class="sr-only peer feature-toggle" 
                               data-feature-id="${feature.id}"
                               data-group-number="${groupNumber}"
                               ${feature.isEnabled ? 'checked' : ''}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        <span class="ml-3 text-sm font-medium text-gray-900">${feature.isEnabled ? '已禁用' : '已启用'}</span>
                    </label>
                </div>
            </div>
        `;
        featureList.appendChild(featureCard);
    });

    // 绑定开关事件
    document.querySelectorAll('.feature-toggle').forEach(toggle => {
        toggle.addEventListener('change', async (e) => {
            await toggleFeature(
                e.target.dataset.groupNumber,
                e.target.dataset.featureId,
                e.target.checked
            );
        });
    });
}

// 切换功能状态
async function toggleFeature(groupNumber, featureId, enabled) {
    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录');
        }

        const response = await fetch(`/user/group/${groupNumber}/blacklist/toggle`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${user.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                featureId: parseInt(featureId),
                enabled: enabled
            })
        });

        if (!response.ok) {
            throw new Error('操作失败');
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.message || '操作失败');
        }

        // 更新UI状态
        const toggle = document.querySelector(`[data-feature-id="${featureId}"]`);
        const statusText = toggle.parentElement.querySelector('span');
        statusText.textContent = enabled ? '已禁用' : '已启用';

    } catch (error) {
        console.error('切换功能状态失败:', error);
        alert('操作失败: ' + error.message);
        // 还原开关状态
        const toggle = document.querySelector(`[data-feature-id="${featureId}"]`);
        toggle.checked = !toggle.checked;
    }
}

// 显示修改密码模态框
function showChangePasswordModal() {
    const modal = document.getElementById('changePasswordModal');
    const form = document.getElementById('changePasswordForm');
    const error = document.getElementById('changePasswordError');

    // 重置表单
    form.reset();
    error.textContent = '';

    // 显示模态框
    modal.classList.remove('hidden');
}

// 处理修改密码表单提交
document.getElementById('changePasswordForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    const oldPassword = document.getElementById('oldPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const qqVerify = document.getElementById('qqVerify').value.trim();
    const errorDiv = document.getElementById('changePasswordError');
    const submitButton = document.getElementById('submitPasswordChange');

    // 清除之前的错误信息
    errorDiv.textContent = '';

    // 验证新密码和确认密码是否匹配
    if (newPassword !== confirmPassword) {
        errorDiv.textContent = '两次输入的新密码不一致';
        return;
    }

    // 验证密码长度
    if (newPassword.length < 6) {
        errorDiv.textContent = '新密码长度必须至少6位';
        return;
    }

    // 显示加载状态
    submitButton.disabled = true;
    submitButton.querySelector('.button-text').textContent = '处理中...';
    submitButton.querySelector('.loader').classList.remove('hidden');

    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录，请重新登录');
        }

        const response = await fetch('/user/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${user.token}`
            },
            body: JSON.stringify({
                oldPassword: oldPassword,
                newPassword: newPassword,
                qqVerify: qqVerify
            })
        });

        const result = await response.json();

        if (!response.ok || !result.success) {
            throw new Error(result.message || '修改密码失败');
        }

        // 修改成功
        alert('密码修改成功！请使用新密码重新登录。');

        // 清除本地存储的登录信息
        localStorage.removeItem('user');

        // 关闭模态框并刷新页面
        document.getElementById('changePasswordModal').classList.add('hidden');

        // 重定向到登录页面
        setTimeout(() => {
            window.location.reload();
        }, 1000);

    } catch (error) {
        console.error('修改密码失败:', error);
        errorDiv.textContent = error.message;
    } finally {
        // 恢复按钮状态
        submitButton.disabled = false;
        submitButton.querySelector('.button-text').textContent = '确认修改';
        submitButton.querySelector('.loader').classList.add('hidden');
    }
});

// 关闭修改密码模态框
document.getElementById('closePasswordModal').addEventListener('click', () => {
    document.getElementById('changePasswordModal').classList.add('hidden');
});

document.getElementById('cancelPasswordChange').addEventListener('click', () => {
    document.getElementById('changePasswordModal').classList.add('hidden');
});

// 修改绑定成功后的提示
bindForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    const orderNumber = bindOrderNumberInput.value.trim();
    const groupNumber = bindGroupNumberInput.value.trim();
    if (!orderNumber || !groupNumber) {
        showModal('输入错误', '', '激活码和绑定群号均不能为空！');
        return;
    }
    toggleButtonLoading(bindButton, true);
    try {
        // 获取用户token
        const user = JSON.parse(localStorage.getItem('user'));
        const token = user ? user.token : null;

        if (!token) {
            showModal('未登录', '', '您需要先登录才能绑定激活码');
            setTimeout(() => {
                hideModal();
                showSection('loginSection');
            }, 1500);
            return;
        }

        const response = await fetch('/bind', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ orderNumber, groupNumber })
        });
        const result = await response.json();
        if (response.ok && result.success) {
            const actionText = result.data.isRenewal ? '续费' : '绑定';

            // 检查是否包含按次付费功能
            const skuType = result.data.skuType.toLowerCase();
            const hasPayPerUse = skuType.includes('按次') || skuType.includes('pay') || skuType.includes('usage');

            const content = `
                <div class="space-y-4">
                    <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                        <p><strong>操作类型:</strong> ${actionText}</p>
                        <p><strong>激活码:</strong> ${escapeHtml(result.data.orderNumber)}</p>
                        <p><strong>绑定群号:</strong> ${escapeHtml(result.data.groupNumber)}</p>
                        <p><strong>档位类型:</strong> ${escapeHtml(result.data.skuType)}</p>
                        <p><strong>到期时间:</strong> ${escapeHtml(result.data.expirationDate)}</p>
                        ${result.data.isRenewal ? '<p class="mt-2 text-green-600 font-medium">✓ 续费成功，有效期已延长</p>' : ''}
                    </div>

                    ${hasPayPerUse ? `
                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <h4 class="font-medium text-blue-900 mb-2">🎉 发现按次付费功能！</h4>
                        <p class="text-sm text-blue-700 mb-2">
                            您的套餐包含按次付费功能，可以为群聊充值各种功能的按次使用次数。
                        </p>
                        <div class="flex gap-2">
                            <button onclick="showPayPerUseSection()" class="bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-1 rounded">
                                立即查看
                            </button>
                            <button onclick="showPayPerUseRedeemTab()" class="bg-green-600 hover:bg-green-700 text-white text-sm px-3 py-1 rounded">
                                使用兑换码
                            </button>
                        </div>
                    </div>
                    ` : ''}

                    <div class="text-sm text-gray-600">
                        <p>绑定成功后，您可以在"我的绑定"中管理此群聊的功能设置。</p>
                    </div>
                </div>
            `;
            showModal(`🎉 ${actionText}成功`, content);
            bindForm.reset();

            // 绑定成功后刷新激活记录列表
            setTimeout(async () => {
                hideModal();
                await showUserMainSection();
                // renderUserOrders(); // 如需在用户中心立即显示激活记录可解注
            }, hasPayPerUse ? 3000 : 1500); // 如果有按次付费功能，延长显示时间
        } else {
            showModal('操作失败', '', result.message || `服务请求错误 (状态: ${response.status})`);
        }
    } catch (error) {
        console.error('绑定激活码时发生错误:', error);
        showModal('网络错误', '', '绑定请求失败，请检查您的网络连接或稍后再试。');
    } finally {
        toggleButtonLoading(bindButton, false);
    }
});

// 初始化页面
async function initPage() {
    if (checkLoginStatus()) {
        await showUserMainSection();
        // await renderUserOrders(); // 如需加载激活记录可解注
    } else {
        showSection('initialView');
    }
}

// 登录功能
loginForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value.trim();

    if (!username || !password) {
        showModal('输入错误', '', '用户名和密码均不能为空！');
        return;
    }

    toggleButtonLoading(loginButton, true);

    try {
        const response = await fetch('/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });
        const result = await response.json();

        if (response.ok && result.success) {
            // 保存用户信息到本地存储，包括QQ号
            const userData = {
                username: result.data.username,
                email: result.data.email,
                qq: result.data.qq,  // 添加QQ号
                token: result.data.token,
                registerTime: result.data.registerTime
            };
            localStorage.setItem('user', JSON.stringify(userData));

            // 更新全局变量
            currentUser = userData;
            isLoggedIn = true;
            token = userData.token;

            showModal('登录成功', '<p>您已成功登录，即将跳转到用户中心。</p>');
            setTimeout(async () => {
                hideModal();
                await showUserMainSection();
            }, 1500);
        } else {
            showModal('登录失败', '', result.message || '用户名或密码错误');
        }
    } catch (error) {
        console.error('登录时发生错误:', error);
        showModal('网络错误', '', '登录请求失败，请检查您的网络连接或稍后再试。');
    } finally {
        toggleButtonLoading(loginButton, false);
    }
});

// 注册功能
registerForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    const username = document.getElementById('registerUsername').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const qq = document.getElementById('registerQQ').value.trim();  // 获取QQ号
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('registerConfirmPassword').value;
    const captcha = (document.getElementById('registerEmailCaptcha') && !document.getElementById('registerCaptchaRow').classList.contains('hidden'))
        ? document.getElementById('registerEmailCaptcha').value.trim() : '';

    // 验证密码匹配
    if (password !== confirmPassword) {
        showModal('注册失败', '两次输入的密码不一致', true);
        return;
    }

    // 验证QQ号格式
    if (!/^\d{5,18}$/.test(qq)) {
        showModal('注册失败', '请输入正确的QQ号（5-18位数字）', true);
        return;
    }

    toggleButtonLoading(registerButton, true);

    try {
        const response = await fetch('/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, email, qq, password, captcha })
        });

        const data = await response.json();

        if (response.ok && data.success) {
            showModal('注册成功', `
                <p>您的账户已成功创建！</p>
                <p class="mt-2 text-sm text-gray-600">用户名：${escapeHtml(data.data.username)}</p>
                <p class="text-sm text-gray-600">邮箱：${escapeHtml(data.data.email)}</p>
                <p class="text-sm text-gray-600">QQ：${escapeHtml(data.data.qq)}</p>
                <p class="mt-3 text-sm text-gray-500">请登录以继续操作。</p>
            `, false);

            registerForm.reset();

            // 注册成功后直接刷新页面，避免UI卡顿
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showModal('注册失败', data.message || '注册过程中发生错误', true);
        }
    } catch (error) {
        console.error('注册错误:', error);
        showModal('注册失败', '网络错误，请稍后重试', true);
    } finally {
        toggleButtonLoading(registerButton, false);
    }
});

// 退出登录功能
function logout() {
    localStorage.removeItem('user');

    // 重置全局变量
    isLoggedIn = false;
    token = null;
    currentUser = null;

    showModal('退出成功', '<p>您已成功退出登录。</p>');
    setTimeout(() => {
        hideModal();
        showSection('initialView');
    }, 1500);
}

// 按钮事件监听设置函数
function setupEventListeners() {
    // 获取DOM元素
    initialView = document.getElementById('initialView');

    // 获取模态框相关元素
    modal = document.getElementById('resultModal');
    modalTitle = document.getElementById('modalTitle');
    modalBody = document.getElementById('modalBody');
    modalError = document.getElementById('modalError');
    closeModalButton = document.getElementById('closeModalButton');
    modalOkButton = document.getElementById('modalOkButton');

    console.log('模态框元素获取结果:', {
        modal: !!modal,
        modalTitle: !!modalTitle,
        modalBody: !!modalBody,
        modalError: !!modalError,
        closeModalButton: !!closeModalButton,
        modalOkButton: !!modalOkButton
    });
    bindSection = document.getElementById('bindSection');
    loginSection = document.getElementById('loginSection');
    registerSection = document.getElementById('registerSection');
    userPanel = document.getElementById('userPanel');

    showBindButton = document.getElementById('showBindButton');
    showLoginButton = document.getElementById('showLoginButton');
    showRegisterButton = document.getElementById('showRegisterButton');
    showPayPerUseButton = document.getElementById('showPayPerUseButton');

    switchToLoginLink = document.getElementById('switchToLogin');
    switchToRegisterLink = document.getElementById('switchToRegister');
    userPanelBindButton = document.getElementById('userPanelBindButton');
    logoutButton = document.getElementById('logoutButton');

    // 绑定登录按钮事件
    if (showLoginButton) {
        console.log('绑定登录按钮事件');
        showLoginButton.addEventListener('click', (event) => {
            console.log('登录按钮被点击', event);
            showSection('loginSection');
        });
    } else {
        console.error('未找到登录按钮');
    }

    // 绑定注册按钮事件
    if (showRegisterButton) {
        console.log('绑定注册按钮事件');
        showRegisterButton.addEventListener('click', (event) => {
            console.log('注册按钮被点击', event);
            showSection('registerSection');
        });
    } else {
        console.error('未找到注册按钮');
    }

    if (showBindButton) {
        showBindButton.addEventListener('click', () => showSection('bindSection'));
    }

    // 绑定按次付费按钮事件
    if (showPayPerUseButton) {
        showPayPerUseButton.addEventListener('click', async () => {
            // 先检查用户是否有激活的绑定记录
            if (!isLoggedIn) {
                showModal('提示', '', '请先登录后再使用按次付费功能');
                return;
            }

            try {
                const response = await fetch('/user/bindings', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    const bindings = result.data || [];
                    const hasActiveBindings = bindings.some(binding => {
                        if (!binding.expirationDate || binding.expirationDate.includes('永久')) {
                            return true;
                        }
                        return new Date(binding.expirationDate) > new Date();
                    });

                    if (hasActiveBindings) {
                        console.log('用户有激活绑定，显示按次付费页面');
                        hideAllSections();
                        const section = document.getElementById('payPerUseSection');
                        console.log('按次付费区域元素:', section);
                        if (section) {
                            section.classList.remove('hidden');
                            section.classList.add('active');
                            console.log('按次付费区域类名:', section.className);

                            // 默认显示余额查询标签
                            const balanceTab = document.getElementById('payPerUseBalanceTab');
                            const balanceContent = document.getElementById('payPerUseBalanceContent');
                            console.log('标签元素:', { balanceTab: !!balanceTab, balanceContent: !!balanceContent });

                            if (balanceTab && balanceContent) {
                                // 激活余额查询标签
                                document.querySelectorAll('.pay-per-use-user-tab').forEach(tab => tab.classList.remove('active'));
                                document.querySelectorAll('.pay-per-use-user-content').forEach(content => {
                                    content.classList.add('hidden');
                                    content.classList.remove('active-content');
                                });
                                balanceTab.classList.add('active');
                                balanceContent.classList.remove('hidden');
                                balanceContent.classList.add('active-content');
                                console.log('余额内容区域类名:', balanceContent.className);
                                console.log('余额标签类名:', balanceTab.className);
                            }
                            // 加载按次付费数据
                            loadPayPerUseBalance();

                            // 简洁的显示逻辑 - 添加标签切换功能
                            console.log('显示按次付费页面');

                            // 添加标签切换事件
                            setTimeout(() => {
                                const balanceTab = document.getElementById('payPerUseBalanceTab');
                                const logsTab = document.getElementById('payPerUseLogsTab');
                                const redeemTab = document.getElementById('payPerUseRedeemTab');
                                const balanceContent = document.getElementById('payPerUseBalanceContent');
                                const logsContent = document.getElementById('payPerUseLogsContent');
                                const redeemContent = document.getElementById('payPerUseRedeemContent');

                                if (balanceTab && logsTab && redeemTab) {
                                    balanceTab.onclick = () => {
                                        switchPayPerUseUserTab(balanceTab, balanceContent);
                                        loadPayPerUseBalance();
                                    };

                                    logsTab.onclick = () => {
                                        switchPayPerUseUserTab(logsTab, logsContent);
                                        loadPayPerUseLogs();
                                    };

                                    redeemTab.onclick = () => {
                                        switchPayPerUseUserTab(redeemTab, redeemContent);
                                        loadPayPerUseRedeem();
                                    };

                                    console.log('✅ 按次付费标签切换功能已添加');
                                }
                            }, 100);
                        } else {
                            console.error('未找到按次付费区域元素');
                        }
                    } else {
                        showModal('提示', '', '您没有激活的群聊绑定，无法使用按次付费功能。请先绑定激活码到群聊。');
                    }
                } else {
                    showModal('提示', '', '获取绑定信息失败，请稍后重试');
                }
            } catch (error) {
                console.error('检查绑定状态失败:', error);
                showModal('提示', '', '网络错误，请稍后重试');
            }
        });
    }

    backFromBindButton.addEventListener('click', async () => await showUserMainSection());
    backFromLoginButton.addEventListener('click', () => showSection('initialView'));
    backFromRegisterButton.addEventListener('click', () => showSection('initialView'));

    // 用户信息页面返回按钮
    const backToMainFromUserInfo = document.getElementById('backToMainFromUserInfo');
    if (backToMainFromUserInfo) {
        backToMainFromUserInfo.addEventListener('click', async () => await showUserMainSection());
    }

    if (switchToLoginLink) {
        switchToLoginLink.addEventListener('click', (e) => {
            e.preventDefault();
            showSection('loginSection');
        });
    }

    if (switchToRegisterLink) {
        switchToRegisterLink.addEventListener('click', (e) => {
            e.preventDefault();
            showSection('registerSection');
        });
    }
}

// 发送注册邮箱验证码
const sendRegisterCaptchaButton = document.getElementById('sendRegisterCaptchaButton');
if (sendRegisterCaptchaButton) {
    sendRegisterCaptchaButton.addEventListener('click', async () => {
        try {
            const email = document.getElementById('registerEmail')?.value.trim();
            if (!email) {
                showModal('提示', '请先填写邮箱', true);
                return;
            }
            sendRegisterCaptchaButton.disabled = true;
            const resp = await fetch('/register/email-captcha', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email })
            });
            const result = await resp.json().catch(() => ({ success: false }));
            if (resp.ok && result.success) {
                showModal('验证码已发送', '请在10分钟内完成验证', false);
                let seconds = 60;
                const originText = sendRegisterCaptchaButton.textContent;
                sendRegisterCaptchaButton.textContent = `重新发送(${seconds}s)`;
                const timer = setInterval(() => {
                    seconds -= 1;
                    if (seconds > 0) {
                        sendRegisterCaptchaButton.textContent = `重新发送(${seconds}s)`;
                    } else {
                        clearInterval(timer);
                        sendRegisterCaptchaButton.textContent = originText;
                        sendRegisterCaptchaButton.disabled = false;
                    }
                }, 1000);
            } else {
                sendRegisterCaptchaButton.disabled = false;
                showModal('发送失败', result.message || '请稍后重试', true);
            }
        } catch (e) {
            sendRegisterCaptchaButton.disabled = false;
            showModal('发送失败', '网络异常，请稍后重试', true);
        }
    });

    // 用户面板绑定按钮事件
    if (userPanelBindButton) {
        userPanelBindButton.addEventListener('click', () => showSection('bindSection'));
    }

    // 登出按钮事件
    if (logoutButton) {
        logoutButton.addEventListener('click', logout);
    }

    // 延迟绑定模态框事件监听，确保DOM元素已经存在
    setTimeout(() => {
        // 重新获取模态框元素
        modal = document.getElementById('resultModal');
        modalTitle = document.getElementById('modalTitle');
        modalBody = document.getElementById('modalBody');
        modalError = document.getElementById('modalError');
        closeModalButton = document.getElementById('closeModalButton');
        modalOkButton = document.getElementById('modalOkButton');

        console.log('延迟获取模态框元素结果:', {
            modal: !!modal,
            modalTitle: !!modalTitle,
            modalBody: !!modalBody,
            modalError: !!modalError,
            closeModalButton: !!closeModalButton,
            modalOkButton: !!modalOkButton
        });

        // 模态框事件监听
        if (closeModalButton) {
            closeModalButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('关闭按钮被点击');
                hideModal();
            });
        } else {
            console.error('延迟获取仍未找到关闭按钮 closeModalButton');
        }

        if (modalOkButton) {
            modalOkButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('好的按钮被点击');
                hideModal();
            });
        } else {
            console.error('延迟获取仍未找到好的按钮 modalOkButton');
        }

        if (modal) {
            modal.addEventListener('click', (event) => {
                if (event.target === modal) {
                    console.log('模态框背景被点击');
                    hideModal();
                }
            });
        } else {
            console.error('延迟获取仍未找到模态框 modal');
        }

        // 添加键盘ESC关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal && !modal.classList.contains('hidden')) {
                console.log('ESC键关闭模态框');
                hideModal();
            }
        });
    }, 100);
}



// 工单相关函数
async function loadTicketTypes() {
    try {
        const response = await fetch('/tickets/types');
        const result = await response.json();

        if (result.success) {
            ticketTypes = result.data;
            updateTicketTypeSelect();
        }
    } catch (error) {
        console.error('加载工单类型失败:', error);
    }
}

function updateTicketTypeSelect() {
    const select = document.getElementById('ticketType');
    if (!select) return;

    // 清空现有选项
    select.innerHTML = '<option value="">请选择工单类型</option>';

    // 添加工单类型
    ticketTypes.forEach(type => {
        const option = document.createElement('option');
        option.value = type.id;
        option.textContent = type.name;
        select.appendChild(option);
    });
}

function showTicketsSection() {
    hideAllSections();
    const section = document.getElementById('ticketsSection');
    section.classList.remove('hidden');
    section.classList.add('active');
    loadMyTickets();
}

async function loadMyTickets() {
    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }

        const response = await fetch('/tickets/my', {
            headers: {
                'Authorization': `Bearer ${user.token}`
            }
        });

        // 后端统一404时返回JSON，但防御性处理
        const result = await response.json().catch(() => ({ success: false, message: '响应解析失败' }));

        if (result.success) {
            displayTicketsList(result.data);
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('加载工单列表失败:', error);
        showMessage('加载工单列表失败', 'error');
    }
}

function displayTicketsList(tickets) {
    const ticketsList = document.getElementById('ticketsList');
    const noTicketsMessage = document.getElementById('noTicketsMessage');

    if (tickets.length === 0) {
        ticketsList.classList.add('hidden');
        noTicketsMessage.classList.remove('hidden');
        return;
    }

    ticketsList.classList.remove('hidden');
    noTicketsMessage.classList.add('hidden');

    ticketsList.innerHTML = tickets.map(ticket => `
        <div class="bg-white p-4 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors cursor-pointer" 
             onclick="showTicketDetail(${ticket.id})">
            <div class="flex justify-between items-start mb-2">
                <h3 class="font-medium text-gray-900">${ticket.title}</h3>
                <span class="px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(ticket.status)}">
                    ${getStatusText(ticket.status)}
                </span>
            </div>
            <div class="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                <span>类型: ${ticket.typeName}</span>
                <span>优先级: ${getPriorityText(ticket.priority)}</span>
                <span>回复: ${ticket.replyCount}</span>
            </div>
            <div class="flex items-center justify-between text-xs text-gray-500">
                <span>工单号: ${ticket.ticketNumber}</span>
                <span>创建时间: ${formatDateTime(ticket.createdTime)}</span>
            </div>
        </div>
    `).join('');
}

function getStatusBadgeClass(status) {
    const statusClasses = {
        'open': 'bg-blue-100 text-blue-800',
        'in_progress': 'bg-yellow-100 text-yellow-800',
        'waiting': 'bg-orange-100 text-orange-800',
        'closed': 'bg-gray-100 text-gray-800',
        'reopened': 'bg-purple-100 text-purple-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
}

function getStatusText(status) {
    const statusTexts = {
        'open': '待处理',
        'in_progress': '处理中',
        'waiting': '等待回复',
        'closed': '已关闭',
        'reopened': '重新打开'
    };
    return statusTexts[status] || '未知';
}

function getPriorityText(priority) {
    const priorityTexts = {
        'low': '低',
        'normal': '普通',
        'high': '高'
    };
    return priorityTexts[priority] || '普通';
}

function showCreateTicketForm() {
    hideAllSections();
    const section = document.getElementById('createTicketSection');
    section.classList.remove('hidden');
    section.classList.add('active');
}

async function createTicket(formData) {
    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }

        const response = await fetch('/tickets/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${user.token}`
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showMessage('工单创建成功', 'success');
            showTicketsSection();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('创建工单失败:', error);
        showMessage('创建工单失败', 'error');
    }
}

async function showTicketDetail(ticketId) {
    currentTicketId = ticketId;

    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }

        const response = await fetch(`/tickets/${ticketId}`, {
            headers: {
                'Authorization': `Bearer ${user.token}`
            }
        });

        const result = await response.json();

        if (result.success) {
            displayTicketDetail(result.data);
            // 显示工单详情页（不自动跳转到回复页）
            hideAllSections();
            const section = document.getElementById('ticketDetailSection');
            section.classList.remove('hidden');
            section.classList.add('active');
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('获取工单详情失败:', error);
        showMessage('获取工单详情失败', 'error');
    }
}

function displayTicketDetail(ticket) {
    const content = document.getElementById('ticketDetailContent');

    content.innerHTML = `
        <div class="bg-white p-6 rounded-lg border border-gray-200">
            <div class="flex justify-between items-start mb-4">
                <h3 class="text-xl font-semibold text-gray-900">${ticket.title}</h3>
                <div class="flex space-x-2">
                    <span class="px-3 py-1 text-sm rounded-full ${getStatusBadgeClass(ticket.status)}">
                        ${getStatusText(ticket.status)}
                    </span>
                    <span class="px-3 py-1 text-sm rounded-full ${getPriorityBadgeClass(ticket.priority)}">
                        ${getPriorityText(ticket.priority)}
                    </span>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 text-sm text-gray-600">
                <div>
                    <span class="font-medium">工单号:</span> ${ticket.ticketNumber}
                </div>
                <div>
                    <span class="font-medium">类型:</span> ${ticket.typeName}
                </div>
                <div>
                    <span class="font-medium">创建时间:</span> ${formatDateTime(ticket.createdTime)}
                </div>
                <div>
                    <span class="font-medium">更新时间:</span> ${formatDateTime(ticket.updatedTime)}
                </div>
            </div>
            
            ${ticket.groupNumber ? `
                <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                    <span class="font-medium text-blue-900">相关群号:</span> ${ticket.groupNumber}
                </div>
            ` : ''}
            
            <div class="prose max-w-none">
                <h4 class="font-medium text-gray-900 mb-2">问题描述:</h4>
                <div class="ticket-content text-gray-700 whitespace-pre-wrap break-words overflow-x-auto" style="word-break: break-word; overflow-wrap: anywhere;">${renderMarkdown(ticket.content)}</div>
            </div>
            <div class="mt-6 flex justify-between">
                <button id="backToTicketsFromDetailButtonBottom" class="text-blue-600 hover:text-blue-800 font-medium">返回工单列表</button>
                <button id="goToReplyButton" class="text-blue-600 hover:text-blue-800 font-medium">去回复</button>
            </div>
        </div>
    `;

    // 绑定顶部与底部两个返回按钮（避免重复ID导致底部无效）
    const backBtnTop = document.getElementById('backToTicketsFromDetailButton');
    if (backBtnTop) backBtnTop.addEventListener('click', showTicketsSection);
    const backBtnBottom = document.getElementById('backToTicketsFromDetailButtonBottom');
    if (backBtnBottom) backBtnBottom.addEventListener('click', showTicketsSection);
    const replyBtn = document.getElementById('goToReplyButton');
    if (replyBtn) replyBtn.addEventListener('click', showTicketReplySection);

    // 启用图片点击预览（正文区域）
    enableImagePreview(content);
}

function getPriorityBadgeClass(priority) {
    const priorityClasses = {
        'low': 'bg-green-100 text-green-800',
        'normal': 'bg-blue-100 text-blue-800',
        'high': 'bg-red-100 text-red-800'
    };
    return priorityClasses[priority] || 'bg-blue-100 text-blue-800';
}

function showTicketReplySection() {
    if (!currentTicketId) {
        showModal('提示', '', '未选择工单，无法加载回复');
        showTicketsSection();
        return;
    }
    hideAllSections();
    const section = document.getElementById('ticketReplySection');
    section.classList.remove('hidden');
    section.classList.add('active');
    loadTicketReplies();
}

async function loadTicketReplies() {
    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }

        const response = await fetch(`/tickets/${currentTicketId}/replies`, {
            headers: {
                'Authorization': `Bearer ${user.token}`
            }
        });

        let result;
        try {
            result = await response.json();
        } catch (e) {
            // 若命中老接口/反向代理异常，再试一次兼容路径
            const retry = await fetch(`/tickets/${currentTicketId}/replyList`, {
                headers: { 'Authorization': `Bearer ${user.token}` }
            });
            result = await retry.json();
        }

        if (result.success) {
            displayTicketReplies(result.data);
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('加载工单回复失败:', error);
        showMessage('加载工单回复失败', 'error');
    }
}

function displayTicketReplies(replies) {
    const repliesContainer = document.getElementById('ticketReplies');

    if (replies.length === 0) {
        repliesContainer.innerHTML = '<p class="text-gray-500 text-center py-4">暂无回复</p>';
        return;
    }

    repliesContainer.innerHTML = replies.map(reply => `
        <div class="reply-card bg-white p-4 rounded-lg border border-gray-200 overflow-hidden">
            <div class="flex justify-between items-start mb-2">
                <div class="flex items-center space-x-2">
                    <span class="font-medium text-gray-900">${reply.isAdmin ? '管理员' : '用户'}</span>
                    <span class="text-sm text-gray-500">${formatDateTime(reply.createdTime)}</span>
                </div>
            </div>
            <div class="prose max-w-none">
                <div class="ticket-content text-gray-700 whitespace-pre-wrap break-words overflow-x-auto" style="word-break: break-word; overflow-wrap: anywhere;">${renderMarkdown(reply.content)}</div>
            </div>
        </div>
    `).join('');

    // 启用图片点击预览（回复区域）
    enableImagePreview(repliesContainer);
}

async function submitTicketReply(content) {
    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }

        const response = await fetch(`/tickets/${currentTicketId}/reply`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${user.token}`
            },
            body: JSON.stringify({ content })
        });

        const result = await response.json();

        if (result.success) {
            showModal('提交成功', '<p>您的回复已成功提交</p>');
            // 清空输入框
            document.getElementById('replyContent').value = '';
            // 重新加载回复列表
            loadTicketReplies();
        } else {
            showModal('提交失败', '', result.message);
        }
    } catch (error) {
        console.error('提交回复失败:', error);
        showModal('网络错误', '', '提交回复失败，请检查网络连接后重试');
    }
}

// 上传当前工单附件（不附带回复文本，直接挂到工单）
async function uploadCurrentTicketAttachment(file) {
    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }
        if (!currentTicketId) {
            throw new Error('请先打开一个工单');
        }
        const formData = new FormData();
        formData.append('file', file);
        const resp = await fetch(`/tickets/${currentTicketId}/attachments`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${user.token}`
            },
            body: formData
        });
        const result = await resp.json();
        if (!resp.ok || !result.success) {
            throw new Error(result.message || '附件上传失败');
        }
        // 自动发表一条引用该附件的回复（若是图片则直接以Markdown插入）
        const uploaded = result.data || {};
        const u = JSON.parse(localStorage.getItem('user')) || {};
        const token = u.token || '';
        // 受控文件访问接口，图片标签无法带头部，因此拼接 token 参数
        const fileUrl = `/files/${uploaded.filePath}?token=${encodeURIComponent(token)}`;
        const isImage = /\.(png|jpe?g|gif|webp|bmp|svg)$/i.test(uploaded.fileName || uploaded.filePath || '');
        const replyText = isImage ? `![${uploaded.fileName || '图片'}](${fileUrl})` : `[${uploaded.fileName || '附件'}](${fileUrl})`;
        await submitTicketReply(replyText);
        showMessage('附件上传成功，已作为回复发布', 'success');
        // 留在回复页并刷新回复列表
        if (document.getElementById('ticketReplySection')) {
            await loadTicketReplies();
        } else {
            showTicketDetail(currentTicketId);
        }
    } catch (err) {
        console.error('附件上传失败:', err);
        showMessage(`附件上传失败: ${err.message}`, 'error');
    }
}

async function closeTicket() {
    if (!confirm('确定要关闭这个工单吗？')) {
        return;
    }

    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }

        const response = await fetch(`/tickets/${currentTicketId}/close`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${user.token}`
            }
        });

        const result = await response.json();

        if (result.success) {
            showMessage('工单已关闭', 'success');
            showTicketsSection();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('关闭工单失败:', error);
        showMessage('关闭工单失败', 'error');
    }
}

function renderMarkdown(text) {
    if (!text) return '';

    // 简单防注入：只在需要的地方插入HTML，其余先做基本转义
    const escape = (s) => String(s)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');

    const sanitizeUrl = (url) => {
        try {
            const u = String(url).trim();
            // 允许 http/https 以及站内相对路径 /...
            if (/^https?:\/\//i.test(u) || /^\//.test(u)) return u;
            return '';
        } catch { return ''; }
    };

    const ensureInternalFileUrlWithToken = (url) => {
        try {
            if (!/^\//.test(url)) return url;
            if (!url.startsWith('/files/')) return url;
            if (url.includes('token=')) return url;
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            const token = user.token || '';
            if (!token) return url;
            return url + (url.includes('?') ? '&' : '?') + 'token=' + encodeURIComponent(token);
        } catch { return url; }
    };

    // 先处理代码块（反引号），避免内部再被替换
    let html = escape(text);

    // 图片 ![alt](url) —— 默认缩略显示，点击放大预览
    html = html.replace(/!\[(.*?)\]\((.*?)\)/g, (m, alt, url) => {
        let safeUrl = sanitizeUrl(url);
        safeUrl = ensureInternalFileUrlWithToken(safeUrl);
        const safeAlt = escape(alt);
        if (!safeUrl) return m;
        return `<img src="${safeUrl}" alt="${safeAlt}" style="max-width:160px;height:auto;border-radius:6px;cursor:zoom-in;" />`;
    });

    // 链接 [text](url)
    html = html.replace(/\[(.*?)\]\((.*?)\)/g, (m, text, url) => {
        let safeUrl = sanitizeUrl(url);
        safeUrl = ensureInternalFileUrlWithToken(safeUrl);
        const safeText = escape(text);
        if (!safeUrl) return safeText;
        return `<a href="${safeUrl}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline break-all">${safeText}</a>`;
    });

    // 加粗、斜体、行内代码
    html = html
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
        .replace(/\n/g, '<br>');

    return html;
}

// 启用图片点击放大预览
function enableImagePreview(rootEl) {
    if (!rootEl) return;
    rootEl.querySelectorAll('img').forEach(img => {
        img.style.cursor = 'zoom-in';
        img.addEventListener('click', () => openImagePreview(img.src));
    });
}

function openImagePreview(src) {
    const modal = document.getElementById('imagePreviewModal');
    const img = document.getElementById('imagePreviewImg');
    if (!modal || !img) return;
    img.src = src;
    modal.classList.remove('hidden');
}

function closeImagePreview() {
    const modal = document.getElementById('imagePreviewModal');
    const img = document.getElementById('imagePreviewImg');
    if (!modal || !img) return;
    img.src = '';
    modal.classList.add('hidden');
}

function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';

    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function hideAllSections() {
    console.log('隐藏所有区域');
    const sections = [
        'initialView', 'bindSection', 'loginSection', 'registerSection',
        'userMainSection', 'bindingsSection', 'ticketsSection',
        'createTicketSection', 'ticketDetailSection', 'ticketReplySection',
        'userPanel', 'userInfoSection', 'payPerUseSection'
    ];

    sections.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
            console.log('隐藏区域:', sectionId, '移除active类');
            section.classList.remove('active');
            section.classList.add('hidden');
            // 清除可能存在的内联样式，确保CSS类生效
            section.style.display = '';
            section.style.visibility = '';
            section.style.opacity = '';
            section.style.height = '';
            section.style.maxHeight = '';
            section.style.overflow = '';
            console.log('区域当前类:', section.className);
        }
    });
}

// 显示用户主界面
async function showUserMainSection() {
    hideAllSections();
    const section = document.getElementById('userMainSection');
    section.classList.remove('hidden');
    section.classList.add('active');

    // 如果公开设置还没有加载，先等待加载
    if (!publicSettings) {
        await loadPublicSettings();
    }

    // 检查用户是否有激活的绑定记录，决定是否显示按次付费按钮
    checkUserBindingsForPayPerUse();
}

// 检查用户绑定状态，控制按次付费按钮显示
async function checkUserBindingsForPayPerUse() {
    if (!isLoggedIn) {
        hidePayPerUseButton();
        return;
    }

    // 首先检查系统设置中是否启用了按次付费功能
    if (!publicSettings || !publicSettings.payPerUseEnabled) {
        hidePayPerUseButton();
        return;
    }

    try {
        const response = await fetch('/user/bindings', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const result = await response.json();

        if (response.ok && result.success) {
            const bindings = result.data || [];
            // 检查是否有未过期的绑定记录
            const hasActiveBindings = bindings.some(binding => {
                if (!binding.expirationDate || binding.expirationDate.includes('永久')) {
                    return true;
                }
                return new Date(binding.expirationDate) > new Date();
            });

            if (hasActiveBindings) {
                showPayPerUseButtonElement();
            } else {
                hidePayPerUseButton();
            }
        } else {
            hidePayPerUseButton();
        }
    } catch (error) {
        console.error('检查用户绑定状态失败:', error);
        hidePayPerUseButton();
    }
}

// 显示按次付费按钮
function showPayPerUseButtonElement() {
    const button = document.getElementById('showPayPerUseButton');
    if (button && publicSettings && publicSettings.payPerUseEnabled) {
        button.classList.remove('hidden');
    }
}

// 隐藏按次付费按钮
function hidePayPerUseButton() {
    const button = document.getElementById('showPayPerUseButton');
    if (button) {
        button.classList.add('hidden');
    }
}

// 新增：显示用户信息页
function showUserInfoSection() {
    hideAllSections();
    const sec = document.getElementById('userInfoSection');
    sec.classList.remove('hidden');
    sec.classList.add('active');
    // 清除任何内联样式，让CSS类控制显示
    sec.style.display = '';
    sec.style.visibility = '';
    sec.style.opacity = '';
    sec.style.height = '';
    sec.style.maxHeight = '';
    sec.style.overflow = '';
    loadUserProfile();
}

async function loadPublicSettings() {
    try {
        const resp = await fetch('/api/settings-public');
        if (resp.ok) {
            const result = await resp.json();
            if (result.success) publicSettings = result.data;
        }
        // 应用网站主题到页面标题
        if (publicSettings && publicSettings.siteTitle) {
            try { document.title = publicSettings.siteTitle; } catch { }
        }
        // 应用背景图设置
        if (publicSettings && publicSettings.backgroundImageApi) {
            try {
                document.body.style.backgroundImage = `url('${publicSettings.backgroundImageApi}')`;
            } catch { }
        }
        // 根据开关隐藏入口
        const showTicketsButtonEl = document.getElementById('showTicketsButton');
        if (showTicketsButtonEl) showTicketsButtonEl.style.display = publicSettings.ticketsEnabled ? '' : 'none';

        // 隐藏按次付费相关入口
        const showPayPerUseButtonEl = document.getElementById('showPayPerUseButton');
        if (showPayPerUseButtonEl) {
            showPayPerUseButtonEl.style.display = publicSettings.payPerUseEnabled ? '' : 'none';
        }

        // 隐藏功能黑名单相关入口（用户主界面和绑定列表内的按钮）
        if (!publicSettings.featureBlacklistEnabled) {
            // 主界面没有独立入口，主要隐藏列表中的按钮
            document.querySelectorAll('button').forEach(btn => {
                if (btn && btn.textContent && btn.textContent.includes('功能黑名单')) {
                    btn.style.display = 'none';
                }
            });
        }
        // 注册验证码行显示
        try {
            const row = document.getElementById('registerCaptchaRow');
            if (row) row.classList.toggle('hidden', !publicSettings.registerEmailVerificationEnabled);
        } catch { }
    } catch (e) {
        // 默认为启用
    }
}

async function loadUserProfile() {
    try {
        const user = JSON.parse(localStorage.getItem('user')) || {};
        if (!user || !user.token) throw new Error('未登录');
        let data = null;
        try {
            const resp = await fetch('/user/profile', { headers: { 'Authorization': `Bearer ${user.token}` } });
            if (resp.ok) {
                const result = await resp.json();
                if (result.success) data = result.data || null;
            }
        } catch { }
        // Fallback 到本地缓存
        if (!data) {
            data = { username: user.username, email: user.email, qq: user.qq, registerTime: user.registerTime, emailNotifyEnabled: true };
        }
        document.getElementById('uiUsername').textContent = data.username || '-';
        document.getElementById('uiEmail').textContent = data.email || '-';
        document.getElementById('uiQQ').textContent = data.qq || '-';
        document.getElementById('uiRegisterTime').textContent = data.registerTime || '-';
        const toggle = document.getElementById('toggleEmailNotify');
        if (toggle) {
            toggle.checked = !!data.emailNotifyEnabled;
            toggle.disabled = false;
            // 移除旧事件，防止重复绑定
            const newToggle = toggle.cloneNode(true);
            toggle.parentNode.replaceChild(newToggle, toggle);
            newToggle.addEventListener('change', async (e) => {
                try {
                    const r = await fetch('/user/email-notify', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${user.token}` },
                        body: JSON.stringify({ enabled: e.target.checked })
                    });
                    const j = await r.json().catch(() => ({ success: false }));
                    if (!r.ok || !j.success) throw new Error((j && j.message) || '保存失败');
                    showMessage('已更新邮件提醒设置', 'success');
                } catch (err) {
                    e.target.checked = !e.target.checked;
                    showMessage('更新失败：' + err.message, 'error');
                }
            });
        }
    } catch (e) {
        showMessage('获取用户信息失败：' + e.message, 'error');
    }
}

// 显示我的绑定
function showBindingsSection() {
    hideAllSections();
    const section = document.getElementById('bindingsSection');
    section.classList.remove('hidden');
    section.classList.add('active');
    if (typeof loadUserBindings === 'function') {
        loadUserBindings();
    }
}

// 返回主菜单
async function backToMain() {
    await showUserMainSection();
}

// 退出登录
function logout() {
    localStorage.removeItem('user');

    // 重置全局变量
    isLoggedIn = false;
    token = null;
    currentUser = null;

    showInitialView();
}

// 显示初始界面
function showInitialView() {
    hideAllSections();
    const section = document.getElementById('initialView');
    section.classList.remove('hidden');
    section.classList.add('active');
}

// 显示消息
function showMessage(message, type = 'info') {
    showModal('提示', `<p>${message}</p>`);
}

// 加载“我的绑定”列表（用户当前群聊与档位）
async function loadUserBindings() {
    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }

        // 获取绑定记录
        const response = await fetch('/user/bindings', {
            headers: {
                'Authorization': `Bearer ${user.token}`
            }
        });
        const result = await response.json();
        if (!response.ok || !result.success) {
            throw new Error(result.message || '获取绑定记录失败');
        }
        const bindings = result.data || [];

        // 获取按次付费余额（仅在功能启用时）
        let payPerUseBalance = [];
        if (publicSettings && publicSettings.payPerUseEnabled) {
            try {
                const balanceResponse = await fetch('/user/pay-per-use/balance', {
                    headers: {
                        'Authorization': `Bearer ${user.token}`
                    }
                });
                const balanceResult = await balanceResponse.json();
                if (balanceResponse.ok && balanceResult.success) {
                    payPerUseBalance = balanceResult.data || [];
                }
            } catch (error) {
                console.warn('获取按次付费余额失败:', error);
            }
        }

        // 创建群号到余额的映射
        const balanceMap = {};
        payPerUseBalance.forEach(group => {
            balanceMap[group.group_number] = group;
        });

        const listEl = document.getElementById('bindingsList');
        const emptyEl = document.getElementById('noBindingsMessage');
        listEl.innerHTML = '';
        if (bindings.length === 0) {
            emptyEl.classList.remove('hidden');
            return;
        }
        emptyEl.classList.add('hidden');

        bindings.forEach(item => {
            const card = document.createElement('div');
            card.className = 'bg-white p-4 rounded-lg shadow border border-gray-200';
            const isExpired = item.expirationDate && !String(item.expirationDate).includes('永久') && new Date(item.expirationDate) < new Date();

            // 计算剩余天数
            let remainingDaysHtml = '';
            if (item.expirationDate && item.expirationDate !== '永久' && item.expirationISOString) {
                const expirationTime = new Date(item.expirationISOString);
                const now = new Date();
                const diffTime = expirationTime.getTime() - now.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays > 0) {
                    if (diffDays <= 7) {
                        remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-orange-100 text-orange-700">剩余${diffDays}天</span>`;
                    } else if (diffDays <= 30) {
                        remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-yellow-100 text-yellow-700">剩余${diffDays}天</span>`;
                    } else {
                        remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-700">剩余${diffDays}天</span>`;
                    }
                } else if (diffDays === 0) {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-700">今日到期</span>`;
                }
            }

            // 获取该群的按次付费余额
            const groupBalance = balanceMap[item.groupNumber];
            const hasPayPerUseBalance = groupBalance && groupBalance.features && groupBalance.features.length > 0;

            // 检查是否包含按次付费功能
            const skuType = item.skuType.toLowerCase();
            const hasPayPerUse = skuType.includes('按次') || skuType.includes('pay') || skuType.includes('usage');

            // 检查按次付费功能是否启用
            const payPerUseEnabled = publicSettings && publicSettings.payPerUseEnabled;

            card.innerHTML = `
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900">${escapeHtml(item.skuType)}</h4>
                        <p class="text-sm text-gray-500 mt-1">群号：<span class="font-medium">${escapeHtml(item.groupNumber)}</span></p>
                        <p class="text-sm text-gray-500">到期：<span class="font-medium">${escapeHtml(item.expirationDate)}</span>${remainingDaysHtml}</p>

                        ${payPerUseEnabled && hasPayPerUseBalance ? `
                        <div class="mt-2 p-2 bg-blue-50 rounded border border-blue-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-blue-900">按次付费余额</span>
                                <span class="text-xs text-blue-600">${groupBalance.features.length}个功能</span>
                            </div>
                            <div class="mt-1 space-y-1">
                                ${groupBalance.features.slice(0, 2).map(feature => `
                                    <div class="text-xs text-blue-700">
                                        ${feature.feature_name} (${feature.tier_name}): ${feature.remaining_count}次
                                    </div>
                                `).join('')}
                                ${groupBalance.features.length > 2 ? `<div class="text-xs text-blue-600">+${groupBalance.features.length - 2}个功能...</div>` : ''}
                            </div>
                        </div>
                        ` : payPerUseEnabled && hasPayPerUse ? `
                        <div class="mt-2 p-2 bg-yellow-50 rounded border border-yellow-200">
                            <div class="text-sm text-yellow-800">
                                <span class="font-medium">按次付费功能可用</span>
                                <span class="text-xs text-yellow-600">可使用兑换码充值</span>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    ${isExpired ? '<span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded ml-2">已过期</span>' : ''}
                </div>

                <div class="mt-3 pt-3 border-t border-gray-100 space-y-2">
                    ${!isExpired && publicSettings.featureBlacklistEnabled ? `
                    <button onclick="manageFeatureBlacklist('${escapeHtml(item.groupNumber)}')"
                            class="w-full text-center bg-gray-600 hover:bg-gray-700 text-white text-sm py-2 px-3 rounded transition-colors">
                        管理功能黑名单
                    </button>
                    ` : ''}

                    ${payPerUseEnabled && (hasPayPerUse || hasPayPerUseBalance) ? `
                    <div class="grid grid-cols-2 gap-2">
                        <button onclick="viewPayPerUseBalance('${escapeHtml(item.groupNumber)}')"
                                class="text-center bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-3 rounded transition-colors">
                            查看余额
                        </button>
                        <button onclick="usePayPerUseCode('${escapeHtml(item.groupNumber)}')"
                                class="text-center bg-green-600 hover:bg-green-700 text-white text-sm py-2 px-3 rounded transition-colors">
                            使用兑换码
                        </button>
                    </div>
                    ` : ''}
                </div>
            `;
            listEl.appendChild(card);
        });
    } catch (err) {
        console.error('加载绑定记录失败:', err);
        const emptyEl = document.getElementById('noBindingsMessage');
        emptyEl.classList.remove('hidden');
        emptyEl.textContent = `获取绑定记录失败：${err.message}`;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function () {
    console.log('DOM 加载完成，开始初始化');

    // 0. 初始化用户信息容器
    const container = document.querySelector('.container') || document.body;
    container.appendChild(userInfoContainer);
    userInfoContainer.style.display = 'none';

    // 确保按次付费区域在主容器内（修复被嵌入模态框导致不可见的问题）
    try {
        const payPerUseEl = document.getElementById('payPerUseSection');
        if (payPerUseEl && container && !container.contains(payPerUseEl)) {
            container.appendChild(payPerUseEl);
        }
    } catch (e) {
        console.warn('调整按次付费区域位置时出错:', e);
    }

    // 1. 设置事件监听器
    setupEventListeners();

    // 2. 读取公开系统设置（必须在用户状态初始化之前）
    await loadPublicSettings();

    // 3. 初始化用户登录状态
    initUserStatus();

    // 4. 页面初始化
    if (typeof initPage === 'function') {
        await initPage();
    }

    // 3. 用户信息按钮
    const showUserInfoButton = document.getElementById('showUserInfoButton');
    if (showUserInfoButton) {
        showUserInfoButton.addEventListener('click', showUserInfoSection);
    }

    // 4. 检查URL参数自动填充激活码
    const urlParams = new URLSearchParams(window.location.search);
    const activationCode = urlParams.get('code');
    if (activationCode) {
        if (typeof checkLoginStatus === 'function' && checkLoginStatus()) {
            showSection('bindSection');
            if (typeof bindOrderNumberInput !== 'undefined') {
                bindOrderNumberInput.value = activationCode;
            }
            document.getElementById('bindSection').scrollIntoView({ behavior: 'smooth' });
        } else {
            showModal('请先登录', '<p>检测到激活码参数，请先登录后再进行绑定。</p>');
            setTimeout(() => {
                hideModal();
                showSection('loginSection');
            }, 2000);
        }
    }

    // 3. 功能黑名单模态框关闭事件
    const closeFeatureModal = document.getElementById('closeFeatureModal');
    const closeFeatureModalButton = document.getElementById('closeFeatureModalButton');
    const featureBlacklistModal = document.getElementById('featureBlacklistModal');
    if (closeFeatureModal) {
        closeFeatureModal.addEventListener('click', () => {
            featureBlacklistModal.classList.add('hidden');
        });
    }
    if (closeFeatureModalButton) {
        closeFeatureModalButton.addEventListener('click', () => {
            featureBlacklistModal.classList.add('hidden');
        });
    }
    if (featureBlacklistModal) {
        featureBlacklistModal.addEventListener('click', (event) => {
            if (event.target === featureBlacklistModal) {
                featureBlacklistModal.classList.add('hidden');
            }
        });
    }

    // 4. 修改密码模态框关闭
    const changePasswordModal = document.getElementById('changePasswordModal');
    if (changePasswordModal) {
        window.addEventListener('click', (e) => {
            if (e.target === changePasswordModal) {
                changePasswordModal.classList.add('hidden');
            }
        });
    }

    // 5. 用户主界面相关事件监听器
    const showBindingsButton = document.getElementById('showBindingsButton');
    if (showBindingsButton) {
        showBindingsButton.addEventListener('click', showBindingsSection);
    }
    const backToMainButton = document.getElementById('backToMainButton');
    if (backToMainButton) {
        backToMainButton.addEventListener('click', async () => await backToMain());
    }
    const logoutButton = document.getElementById('logoutButton');
    if (logoutButton) {
        logoutButton.addEventListener('click', logout);
    }

    // 6. 工单相关事件监听器
    const showTicketsButton = document.getElementById('showTicketsButton');
    if (showTicketsButton) {
        showTicketsButton.addEventListener('click', showTicketsSection);
    }
    const createTicketButton = document.getElementById('createTicketButton');
    if (createTicketButton) {
        createTicketButton.addEventListener('click', showCreateTicketForm);
    }
    const refreshTicketsButton = document.getElementById('refreshTicketsButton');
    if (refreshTicketsButton) {
        refreshTicketsButton.addEventListener('click', loadMyTickets);
    }
    const backToMainFromTicketsButton = document.getElementById('backToMainFromTicketsButton');
    if (backToMainFromTicketsButton) {
        backToMainFromTicketsButton.addEventListener('click', async () => await showUserMainSection());
    }
    const backToTicketsButton = document.getElementById('backToTicketsButton');
    if (backToTicketsButton) {
        backToTicketsButton.addEventListener('click', showTicketsSection);
    }
    const backToTicketsFromDetailButton = document.getElementById('backToTicketsFromDetailButton');
    if (backToTicketsFromDetailButton) {
        backToTicketsFromDetailButton.addEventListener('click', showTicketsSection);
    }
    const backToTicketDetailButton = document.getElementById('backToTicketDetailButton');
    if (backToTicketDetailButton) {
        backToTicketDetailButton.addEventListener('click', () => showTicketDetail(currentTicketId));
    }
    const cancelTicketButton = document.getElementById('cancelTicketButton');
    if (cancelTicketButton) {
        cancelTicketButton.addEventListener('click', showTicketsSection);
    }
    const createTicketForm = document.getElementById('createTicketForm');
    if (createTicketForm) {
        createTicketForm.addEventListener('submit', function (e) {
            e.preventDefault();
            const formData = {
                title: document.getElementById('ticketTitle').value,
                content: document.getElementById('ticketContent').value,
                typeId: parseInt(document.getElementById('ticketType').value),
                priority: document.getElementById('ticketPriority').value,
                groupNumber: document.getElementById('ticketGroup').value
            };
            createTicket(formData);
        });
    }
    // 附件上传（绑定到当前工单）
    const replyFormEl = document.getElementById('replyForm');
    const replyContentInput = document.getElementById('replyContent');
    if (replyFormEl) {
        // 增加一个可选的文件选择器（若HTML缺失，可以动态插入）
        if (!document.getElementById('replyAttachment')) {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.id = 'replyAttachment';
            fileInput.className = 'mt-2';
            replyFormEl.insertBefore(fileInput, replyFormEl.lastElementChild);
        }
        const fileInput = document.getElementById('replyAttachment');
        replyFormEl.addEventListener('change', async function (e) {
            if (e.target === fileInput && fileInput.files && fileInput.files.length > 0) {
                await uploadCurrentTicketAttachment(fileInput.files[0]);
                // 清空选择，避免重复上传同一个文件不触发change
                fileInput.value = '';
            }
        });
    }
    const replyForm = document.getElementById('replyForm');
    if (replyForm) {
        replyForm.addEventListener('submit', function (e) {
            e.preventDefault();
            const content = document.getElementById('replyContent').value;
            if (!content.trim()) {
                showModal('提示', '', '请输入回复内容');
                return;
            }
            submitTicketReply(content);
        });
    }
    const closeTicketButton = document.getElementById('closeTicketButton');
    if (closeTicketButton) {
        closeTicketButton.addEventListener('click', closeTicket);
    }
    // 图片预览模态框交互（点击背景/按钮或按下ESC关闭）
    const imageModal = document.getElementById('imagePreviewModal');
    const closeImageBtn = document.getElementById('closeImagePreview');
    if (imageModal) {
        imageModal.addEventListener('click', (e) => {
            if (e.target === imageModal) {
                closeImagePreview();
            }
        });
    }
    if (closeImageBtn) {
        closeImageBtn.addEventListener('click', closeImagePreview);
    }
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') closeImagePreview();
    });

    // 7. 加载工单类型
    loadTicketTypes();
});

// ==================== 按次付费功能 ====================

// 按次付费相关DOM元素
const payPerUseSection = document.getElementById('payPerUseSection');
const backFromPayPerUse = document.getElementById('backFromPayPerUse');

// 按次付费子标签
const payPerUseBalanceTab = document.getElementById('payPerUseBalanceTab');
const payPerUseLogsTab = document.getElementById('payPerUseLogsTab');
const payPerUseRedeemTab = document.getElementById('payPerUseRedeemTab');

// 按次付费内容区域
const payPerUseBalanceContent = document.getElementById('payPerUseBalanceContent');
const payPerUseLogsContent = document.getElementById('payPerUseLogsContent');
const payPerUseRedeemContent = document.getElementById('payPerUseRedeemContent');

// 按次付费余额相关
const loginForPayPerUseBalance = document.getElementById('loginForPayPerUseBalance');
const payPerUseBalanceData = document.getElementById('payPerUseBalanceData');
const payPerUseBalanceList = document.getElementById('payPerUseBalanceList');

// 按次付费日志相关
const loginForPayPerUseLogs = document.getElementById('loginForPayPerUseLogs');
const payPerUseLogsData = document.getElementById('payPerUseLogsData');
const payPerUseLogsGroupFilter = document.getElementById('payPerUseLogsGroupFilter');
const payPerUseLogsDateFrom = document.getElementById('payPerUseLogsDateFrom');
const payPerUseLogsDateTo = document.getElementById('payPerUseLogsDateTo');
const searchPayPerUseLogsUser = document.getElementById('searchPayPerUseLogsUser');
const payPerUseLogsList = document.getElementById('payPerUseLogsList');
const payPerUseLogsPrevPage = document.getElementById('payPerUseLogsPrevPage');
const payPerUseLogsNextPage = document.getElementById('payPerUseLogsNextPage');
const payPerUseLogsPaginationInfo = document.getElementById('payPerUseLogsPaginationInfo');

// 按次付费兑换码相关
const loginForPayPerUseRedeem = document.getElementById('loginForPayPerUseRedeem');
const payPerUseRedeemData = document.getElementById('payPerUseRedeemData');
const payPerUseRedeemForm = document.getElementById('payPerUseRedeemForm');
const redeemCode = document.getElementById('redeemCode');
const redeemGroupNumber = document.getElementById('redeemGroupNumber');
const redeemButton = document.getElementById('redeemButton');
const userGroupsContent = document.getElementById('userGroupsContent');



// 返回按钮
const backFromPayPerUseBtn = document.getElementById('backFromPayPerUse');
if (backFromPayPerUseBtn) {
    backFromPayPerUseBtn.addEventListener('click', async () => {
        hideAllSections();
        if (isLoggedIn) {
            await showUserMainSection();
        } else {
            const section = document.getElementById('initialView');
            if (section) {
                section.classList.remove('hidden');
                section.classList.add('active');
            }
        }
    });
}

// 按次付费子标签切换
const payPerUseBalanceTabBtn = document.getElementById('payPerUseBalanceTab');
if (payPerUseBalanceTabBtn) {
    payPerUseBalanceTabBtn.addEventListener('click', () => {
        const balanceTab = document.getElementById('payPerUseBalanceTab');
        const balanceContent = document.getElementById('payPerUseBalanceContent');
        if (balanceTab && balanceContent) {
            switchPayPerUseUserTab(balanceTab, balanceContent);
            if (isLoggedIn) {
                loadPayPerUseBalance();
            }
        }
    });
}

const payPerUseLogsTabBtn = document.getElementById('payPerUseLogsTab');
if (payPerUseLogsTabBtn) {
    payPerUseLogsTabBtn.addEventListener('click', () => {
        const logsTab = document.getElementById('payPerUseLogsTab');
        const logsContent = document.getElementById('payPerUseLogsContent');
        if (logsTab && logsContent) {
            switchPayPerUseUserTab(logsTab, logsContent);
            loadPayPerUseLogs(); // 这里会自动检查登录状态并更新显示
        }
    });
}

const payPerUseRedeemTabBtn = document.getElementById('payPerUseRedeemTab');
if (payPerUseRedeemTabBtn) {
    payPerUseRedeemTabBtn.addEventListener('click', () => {
        const redeemTab = document.getElementById('payPerUseRedeemTab');
        const redeemContent = document.getElementById('payPerUseRedeemContent');
        if (redeemTab && redeemContent) {
            switchPayPerUseUserTab(redeemTab, redeemContent);
            loadPayPerUseRedeem(); // 这里会自动检查登录状态并更新显示
        }
    });
}

// 按次付费子标签切换函数
function switchPayPerUseUserTab(activeTab, activeContent) {
    // 移除所有子标签的active类，并清除内联样式
    document.querySelectorAll('.pay-per-use-user-tab').forEach(tab => {
        tab.classList.remove('active');
        // 清除任何内联样式，让CSS类控制显示
        tab.style.background = '';
        tab.style.color = '';
    });

    // 隐藏所有子内容，并清除内联样式
    document.querySelectorAll('.pay-per-use-user-content').forEach(content => {
        content.classList.add('hidden');
        content.classList.remove('active-content');
        // 清除任何内联样式，让CSS类控制显示
        content.style.display = '';
        content.style.visibility = '';
        content.style.opacity = '';
        content.style.height = '';
        content.style.maxHeight = '';
        content.style.overflow = '';
    });

    // 激活当前标签和内容
    activeTab.classList.add('active');
    activeContent.classList.remove('hidden');
    activeContent.classList.add('active-content');
}

// 登录查看余额按钮
const loginForPayPerUseBalanceBtn = document.getElementById('loginForPayPerUseBalance');
if (loginForPayPerUseBalanceBtn) {
    loginForPayPerUseBalanceBtn.addEventListener('click', () => {
        hideAllSections();
        const loginSec = document.getElementById('loginSection');
        if (loginSec) {
            loginSec.classList.remove('hidden');
            loginSec.classList.add('active');
        }
        // 设置登录成功后的回调
        afterLoginCallback = () => {
            hideAllSections();
            const section = document.getElementById('payPerUseSection');
            if (section) {
                section.classList.remove('hidden');
                section.classList.add('active');
                const balanceTab = document.getElementById('payPerUseBalanceTab');
                const balanceContent = document.getElementById('payPerUseBalanceContent');
                if (balanceTab && balanceContent) {
                    switchPayPerUseUserTab(balanceTab, balanceContent);
                    loadPayPerUseBalance();
                }
            }
        };
    });
}

// 登录查看日志按钮
if (loginForPayPerUseLogs) {
    loginForPayPerUseLogs.addEventListener('click', () => {
        hideAllSections();
        loginSection.classList.remove('hidden');
        loginSection.classList.add('active');
        // 设置登录成功后的回调
        afterLoginCallback = () => {
            hideAllSections();
            payPerUseSection.classList.remove('hidden');
            payPerUseSection.classList.add('active');
            switchPayPerUseUserTab(payPerUseLogsTab, payPerUseLogsContent);
            loadPayPerUseLogs();
        };
    });
}

// 登录使用兑换码按钮
if (loginForPayPerUseRedeem) {
    loginForPayPerUseRedeem.addEventListener('click', () => {
        hideAllSections();
        loginSection.classList.remove('hidden');
        loginSection.classList.add('active');
        // 设置登录成功后的回调
        afterLoginCallback = () => {
            hideAllSections();
            payPerUseSection.classList.remove('hidden');
            payPerUseSection.classList.add('active');
            switchPayPerUseUserTab(payPerUseRedeemTab, payPerUseRedeemContent);
            loadPayPerUseRedeem();
        };
    });
}

// 加载按次付费余额
async function loadPayPerUseBalance() {
    console.log('开始加载按次付费余额');

    // 首先更新界面显示状态
    updatePayPerUseBalanceDisplay();

    if (!isLoggedIn || !token) {
        console.log('用户未登录，无法加载余额');
        return;
    }

    try {
        const response = await fetch('/user/pay-per-use/balance', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const result = await response.json();

        if (result.success) {
            renderPayPerUseBalance(result.data);
            const balanceData = document.getElementById('payPerUseBalanceData');
            if (balanceData) {
                balanceData.classList.remove('hidden');
            }
        } else {
            showModal('获取失败', '', '获取余额失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取按次付费余额时出错:', error);
        showModal('网络错误', '', '获取余额失败，请检查网络连接');
    }
}

// 更新按次付费余额显示状态
function updatePayPerUseBalanceDisplay() {
    const loginPrompt = document.querySelector('#payPerUseBalanceContent .text-center');
    const dataSection = document.getElementById('payPerUseBalanceData');

    if (isLoggedIn) {
        // 用户已登录，隐藏登录提示，显示数据区域
        if (loginPrompt) {
            loginPrompt.style.display = 'none';
        }
        if (dataSection) {
            dataSection.classList.remove('hidden');
        }
    } else {
        // 用户未登录，显示登录提示，隐藏数据区域
        if (loginPrompt) {
            loginPrompt.style.display = 'block';
        }
        if (dataSection) {
            dataSection.classList.add('hidden');
        }
    }
}

// 更新按次付费日志显示状态
function updatePayPerUseLogsDisplay() {
    const loginPrompt = document.querySelector('#payPerUseLogsContent .text-center');
    const dataSection = document.getElementById('payPerUseLogsData');

    if (isLoggedIn) {
        // 用户已登录，隐藏登录提示，显示数据区域
        if (loginPrompt) {
            loginPrompt.style.display = 'none';
        }
        if (dataSection) {
            dataSection.classList.remove('hidden');
        }
    } else {
        // 用户未登录，显示登录提示，隐藏数据区域
        if (loginPrompt) {
            loginPrompt.style.display = 'block';
        }
        if (dataSection) {
            dataSection.classList.add('hidden');
        }
    }
}

// 更新按次付费兑换码显示状态
function updatePayPerUseRedeemDisplay() {
    const loginPrompt = document.querySelector('#payPerUseRedeemContent .text-center');
    const dataSection = document.getElementById('payPerUseRedeemData');

    if (isLoggedIn) {
        // 用户已登录，隐藏登录提示，显示数据区域
        if (loginPrompt) {
            loginPrompt.style.display = 'none';
        }
        if (dataSection) {
            dataSection.classList.remove('hidden');
        }
    } else {
        // 用户未登录，显示登录提示，隐藏数据区域
        if (loginPrompt) {
            loginPrompt.style.display = 'block';
        }
        if (dataSection) {
            dataSection.classList.add('hidden');
        }
    }
}

// 渲染按次付费余额
function renderPayPerUseBalance(data) {
    console.log('渲染按次付费余额数据:', data);
    const balanceList = document.getElementById('payPerUseBalanceList');
    console.log('余额列表元素:', balanceList);
    if (!balanceList) {
        console.error('未找到余额列表元素');
        return;
    }

    balanceList.innerHTML = '';

    if (data.length === 0) {
        console.log('没有余额数据，显示空状态');
        balanceList.innerHTML = '<div class="text-center py-8 text-gray-500">暂无按次付费功能余额数据</div>';
        return;
    }

    data.forEach(group => {
        const groupDiv = document.createElement('div');
        groupDiv.className = 'bg-gray-50 p-4 rounded-lg border';

        let featuresHtml = '';
        group.features.forEach(feature => {
            featuresHtml += `
                <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                    <div>
                        <div class="font-medium text-gray-800">${feature.feature_name}</div>
                        <div class="text-sm text-gray-600">${feature.feature_description || '暂无描述'}</div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold text-blue-600">${feature.remaining_count} 次</div>
                        <div class="text-sm text-gray-500">累计购买: ${feature.total_purchased} 次</div>
                    </div>
                </div>
            `;
        });

        groupDiv.innerHTML = `
            <div class="mb-3">
                <div class="font-semibold text-lg text-gray-800">群号: ${group.group_number}</div>
                <div class="text-sm text-gray-600">所有者: ${group.owner_username}</div>
            </div>
            <div class="space-y-2">
                ${featuresHtml}
            </div>
        `;

        balanceList.appendChild(groupDiv);
    });
}

// 加载按次付费使用日志
async function loadPayPerUseLogs(page = 1) {
    // 首先更新界面显示状态
    updatePayPerUseLogsDisplay();

    if (!isLoggedIn || !token) {
        return;
    }

    try {
        const groupFilterEl = document.getElementById('payPerUseLogsGroupFilter');
        const dateFromEl = document.getElementById('payPerUseLogsDateFrom');
        const dateToEl = document.getElementById('payPerUseLogsDateTo');

        const groupFilter = groupFilterEl ? groupFilterEl.value.trim() : '';
        const dateFrom = dateFromEl ? dateFromEl.value : '';
        const dateTo = dateToEl ? dateToEl.value : '';

        let url = `/user/pay-per-use/logs?page=${page}&per_page=10`;
        if (groupFilter) {
            url += `&group_number=${encodeURIComponent(groupFilter)}`;
        }
        if (dateFrom) {
            url += `&date_from=${encodeURIComponent(dateFrom)}`;
        }
        if (dateTo) {
            url += `&date_to=${encodeURIComponent(dateTo)}`;
        }

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const result = await response.json();

        if (result.success) {
            renderPayPerUseLogs(result.data);
            updatePayPerUseLogsPagination(result.pagination);
            const logsData = document.getElementById('payPerUseLogsData');
            if (logsData) {
                logsData.classList.remove('hidden');
            }
        } else {
            showModal('获取失败', '', '获取使用记录失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取按次付费使用记录时出错:', error);
        showModal('网络错误', '', '获取使用记录失败，请检查网络连接');
    }
}

// 渲染按次付费使用日志
function renderPayPerUseLogs(logs) {
    const logsList = document.getElementById('payPerUseLogsList');
    if (!logsList) return;

    logsList.innerHTML = '';

    if (logs.length === 0) {
        logsList.innerHTML = '<div class="text-center py-8 text-gray-500">暂无使用记录</div>';
        return;
    }

    logs.forEach(log => {
        const logDiv = document.createElement('div');
        logDiv.className = 'bg-white p-4 rounded-lg border shadow-sm hover:shadow-md transition-shadow';

        logDiv.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <div class="text-xs text-gray-500 mb-1">群号</div>
                    <div class="font-medium text-gray-800">${log.group_number || '-'}</div>
                </div>
                <div>
                    <div class="text-xs text-gray-500 mb-1">功能名称</div>
                    <div class="font-medium text-blue-600">${log.feature_name || '-'}</div>
                </div>
                <div>
                    <div class="text-xs text-gray-500 mb-1">使用次数</div>
                    <div class="font-semibold text-green-600">${log.usage_count || 1} 次</div>
                </div>
                <div>
                    <div class="text-xs text-gray-500 mb-1">请求备注</div>
                    <div class="text-gray-700">${log.request_note || '-'}</div>
                </div>
                <div>
                    <div class="text-xs text-gray-500 mb-1">使用时间</div>
                    <div class="text-gray-600">${new Date(log.request_time).toLocaleString('zh-CN', {
            year: 'numeric', month: '2-digit', day: '2-digit',
            hour: '2-digit', minute: '2-digit', second: '2-digit'
        })}</div>
                </div>
            </div>
        `;

        logsList.appendChild(logDiv);
    });
}

// 更新按次付费日志分页信息
function updatePayPerUseLogsPagination(pagination) {
    const paginationInfo = document.getElementById('payPerUseLogsPaginationInfo');
    const prevPage = document.getElementById('payPerUseLogsPrevPage');
    const nextPage = document.getElementById('payPerUseLogsNextPage');

    if (paginationInfo) {
        paginationInfo.textContent = `第 ${pagination.page} 页，共 ${pagination.total} 条记录`;
    }
    if (prevPage) {
        prevPage.disabled = pagination.page <= 1;
    }
    if (nextPage) {
        nextPage.disabled = pagination.page >= pagination.pages;
    }
}

// 搜索按次付费日志
const searchPayPerUseLogsBtn = document.getElementById('searchPayPerUseLogsUser');
if (searchPayPerUseLogsBtn) {
    searchPayPerUseLogsBtn.addEventListener('click', () => {
        loadPayPerUseLogs(1);
    });
}

// 分页按钮事件
const prevPageBtn = document.getElementById('payPerUseLogsPrevPage');
if (prevPageBtn) {
    prevPageBtn.addEventListener('click', () => {
        const paginationInfo = document.getElementById('payPerUseLogsPaginationInfo');
        if (paginationInfo) {
            const currentPage = parseInt(paginationInfo.textContent.split(' ')[1]);
            if (currentPage > 1) {
                loadPayPerUseLogs(currentPage - 1);
            }
        }
    });
}

const nextPageBtn = document.getElementById('payPerUseLogsNextPage');
if (nextPageBtn) {
    nextPageBtn.addEventListener('click', () => {
        const paginationInfo = document.getElementById('payPerUseLogsPaginationInfo');
        if (paginationInfo) {
            const currentPage = parseInt(paginationInfo.textContent.split(' ')[1]);
            loadPayPerUseLogs(currentPage + 1);
        }
    });
}

// 加载按次付费兑换码使用界面
async function loadPayPerUseRedeem() {
    // 首先更新界面显示状态
    updatePayPerUseRedeemDisplay();

    if (!isLoggedIn || !token) {
        return;
    }

    try {
        // 加载用户拥有的激活群聊列表
        await loadUserActiveGroups();

        const redeemData = document.getElementById('payPerUseRedeemData');
        if (redeemData) {
            redeemData.classList.remove('hidden');
        }
    } catch (error) {
        console.error('加载兑换码使用界面时出错:', error);
        showModal('加载失败', '', '加载界面失败，请稍后重试');
    }
}

// 加载用户拥有的激活群聊列表（用于兑换码）
async function loadUserActiveGroups() {
    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录');
        }

        const response = await fetch('/user/bindings', {
            headers: {
                'Authorization': `Bearer ${user.token}`
            }
        });

        const result = await response.json();

        if (!response.ok || !result.success) {
            throw new Error(result.message || '获取绑定记录失败');
        }

        const bindings = result.data || [];

        // 只显示激活状态的绑定（未过期）
        const activeBindings = bindings.filter(binding => {
            if (!binding.expirationDate || binding.expirationDate.includes('永久')) {
                return true;
            }
            return new Date(binding.expirationDate) > new Date();
        });

        // 按群号去重并显示
        const groupNumbers = [...new Set(activeBindings.map(binding => binding.groupNumber))];

        const groupsContent = document.getElementById('userGroupsContent');
        if (!groupsContent) return;

        groupsContent.innerHTML = '';

        if (groupNumbers.length === 0) {
            groupsContent.innerHTML = '<p class="text-gray-500 text-center py-4">您还没有激活的群聊绑定，无法使用兑换码。请先绑定激活码到群聊。</p>';
            return;
        }

        groupNumbers.forEach(groupNumber => {
            // 获取该群的绑定信息
            const bindingInfo = activeBindings.find(b => b.groupNumber === groupNumber);

            const groupDiv = document.createElement('div');
            groupDiv.className = 'bg-gray-50 p-3 rounded-lg border cursor-pointer hover:bg-gray-100 transition-colors';
            groupDiv.innerHTML = `
                <div class="flex justify-between items-center">
                    <div class="flex-1">
                        <div class="font-medium text-gray-800">${groupNumber}</div>
                        <div class="text-sm text-gray-600">套餐：${bindingInfo.skuType}</div>
                        <div class="text-sm text-gray-500">到期：${bindingInfo.expirationDate}</div>
                        <div class="text-xs text-blue-600 mt-1">点击选择此群聊</div>
                    </div>
                    <div class="text-blue-600 ml-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>
            `;

            // 点击选择群号
            groupDiv.addEventListener('click', () => {
                const groupInput = document.getElementById('redeemGroupNumber');
                if (groupInput) {
                    groupInput.value = groupNumber;
                }
                // 高亮选中的群聊
                document.querySelectorAll('#userGroupsContent > div').forEach(div => {
                    div.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50');
                });
                groupDiv.classList.add('ring-2', 'ring-blue-500', 'bg-blue-50');
            });

            groupsContent.appendChild(groupDiv);
        });

    } catch (error) {
        console.error('加载用户激活群聊列表失败:', error);
        if (groupsContent) {
            groupsContent.innerHTML = '<p class="text-red-500 text-center py-4">加载群聊列表失败</p>';
        }
    }
}

// 保留原有的loadUserGroups函数用于其他用途
async function loadUserGroups() {
    await loadUserActiveGroups();
}

// 处理兑换码使用表单提交
payPerUseRedeemForm.addEventListener('submit', async (event) => {
    event.preventDefault();

    const codeInput = document.getElementById('redeemCode');
    const groupInput = document.getElementById('redeemGroupNumber');

    const code = codeInput ? codeInput.value.trim() : '';
    const groupNumber = groupInput ? groupInput.value.trim() : '';

    if (!code || !groupNumber) {
        showModal('输入错误', '', '兑换码和群号均不能为空！');
        return;
    }

    const submitBtn = document.getElementById('redeemButton');
    if (submitBtn) {
        toggleButtonLoading(submitBtn, true);
    }

    try {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.token) {
            throw new Error('未登录或登录已过期');
        }

        const response = await fetch('/user/pay-per-use/redeem', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${user.token}`
            },
            body: JSON.stringify({
                code: code,
                group_number: groupNumber
            })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            const data = result.data;

            // 构建兑换结果显示
            let redeemedHtml = '';
            data.redeemed_features.forEach(feature => {
                redeemedHtml += `
                    <div class="bg-green-50 p-3 rounded-lg border border-green-200">
                        <div class="font-medium text-green-800">${feature.feature_name}</div>
                        <div class="text-sm text-green-600">
                            获得: ${feature.usage_count} 次
                        </div>
                    </div>
                `;
            });

            const content = `
                <div class="space-y-4">
                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <div class="font-medium text-blue-900">兑换成功！</div>
                        <div class="text-sm text-blue-700 mt-1">
                            兑换码: ${escapeHtml(data.code)}<br>
                            群号: ${escapeHtml(data.group_number)}
                        </div>
                    </div>

                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">获得的按次付费功能:</h4>
                        <div class="space-y-2">
                            ${redeemedHtml}
                        </div>
                    </div>

                    <div class="text-sm text-gray-600">
                        兑换的次数已添加到您的按次付费余额中，可以在"余额查询"中查看。
                    </div>
                </div>
            `;

            showModal('🎉 兑换成功', content);

            // 清空表单
            const form = document.getElementById('payPerUseRedeemForm');
            if (form) {
                form.reset();
            }

            // 刷新余额显示（如果当前在余额标签页）
            const balanceTab = document.getElementById('payPerUseBalanceTab');
            if (balanceTab && balanceTab.classList.contains('active')) {
                loadPayPerUseBalance();
            }

        } else {
            // 针对按次付费兑换码的特定错误提示
            let errorMessage = result.message || '兑换请求失败';

            // 如果是兑换码相关错误，提供更详细的说明
            if (errorMessage.includes('兑换码') && (errorMessage.includes('不存在') || errorMessage.includes('过期') || errorMessage.includes('无效'))) {
                errorMessage = '兑换码不存在或已过期，若确认兑换码正确请检查兑换码是否属于按次付费的兑换码而非群聊激活的兑换码。';
            }

            showModal('兑换失败', '', errorMessage);
        }

    } catch (error) {
        console.error('兑换按次付费兑换码时出错:', error);
        showModal('网络错误', '', '兑换请求失败，请检查您的网络连接或稍后再试。');
    } finally {
        if (submitBtn) {
            toggleButtonLoading(submitBtn, false);
        }
    }
});

// 显示按次付费界面（用于绑定成功后的跳转）
function showPayPerUseSection() {
    hideModal();
    setTimeout(() => {
        hideAllSections();
        const section = document.getElementById('payPerUseSection');
        if (section) {
            section.classList.remove('hidden');
            section.classList.add('active');
            // 默认显示余额查询
            const balanceTab = document.getElementById('payPerUseBalanceTab');
            const balanceContent = document.getElementById('payPerUseBalanceContent');
            if (balanceTab && balanceContent) {
                switchPayPerUseUserTab(balanceTab, balanceContent);
                loadPayPerUseBalance(); // 这里会自动检查登录状态并更新显示
            }
        }
    }, 100);
}

// 显示按次付费兑换码标签（用于绑定成功后的跳转）
function showPayPerUseRedeemTab() {
    hideModal();
    setTimeout(() => {
        hideAllSections();
        const section = document.getElementById('payPerUseSection');
        if (section) {
            section.classList.remove('hidden');
            section.classList.add('active');
            // 切换到兑换码使用标签
            const redeemTab = document.getElementById('payPerUseRedeemTab');
            const redeemContent = document.getElementById('payPerUseRedeemContent');
            if (redeemTab && redeemContent) {
                switchPayPerUseUserTab(redeemTab, redeemContent);
                if (isLoggedIn) {
                    loadPayPerUseRedeem();
                }
            }
        }
    }, 100);
}

// 从绑定列表查看按次付费余额
function viewPayPerUseBalance(groupNumber) {
    hideAllSections();
    const section = document.getElementById('payPerUseSection');
    if (section) {
        section.classList.remove('hidden');
        section.classList.add('active');
        // 切换到余额查询标签
        const balanceTab = document.getElementById('payPerUseBalanceTab');
        const balanceContent = document.getElementById('payPerUseBalanceContent');
        if (balanceTab && balanceContent) {
            switchPayPerUseUserTab(balanceTab, balanceContent);
            if (isLoggedIn) {
                loadPayPerUseBalance();
            }
        }
    }
}

// 从绑定列表使用按次付费兑换码
function usePayPerUseCode(groupNumber) {
    hideAllSections();
    const section = document.getElementById('payPerUseSection');
    if (section) {
        section.classList.remove('hidden');
        section.classList.add('active');
        // 切换到兑换码使用标签
        const redeemTab = document.getElementById('payPerUseRedeemTab');
        const redeemContent = document.getElementById('payPerUseRedeemContent');
        if (redeemTab && redeemContent) {
            switchPayPerUseUserTab(redeemTab, redeemContent);
            loadPayPerUseRedeem().then(() => {
                // 自动填充群号
                const groupInput = document.getElementById('redeemGroupNumber');
                if (groupInput) {
                    groupInput.value = groupNumber;
                }
                // 高亮对应的群聊
                document.querySelectorAll('#userGroupsContent > div').forEach(div => {
                    if (div.textContent.includes(groupNumber)) {
                        div.classList.add('ring-2', 'ring-blue-500', 'bg-blue-50');
                    } else {
                        div.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50');
                    }
                });
            }); // 这里会自动检查登录状态并更新显示
        }
    }
}

// 初始化用户登录状态
function initUserStatus() {
    try {
        const userData = localStorage.getItem('user');
        if (userData) {
            currentUser = JSON.parse(userData);
            if (currentUser && currentUser.token) {
                isLoggedIn = true;
                token = currentUser.token;
                console.log('用户已登录:', currentUser.username);
            } else {
                isLoggedIn = false;
                token = null;
                currentUser = null;
            }
        } else {
            isLoggedIn = false;
            token = null;
            currentUser = null;
        }
    } catch (error) {
        console.error('初始化用户状态失败:', error);
        isLoggedIn = false;
        token = null;
        currentUser = null;
    }
}

// 暴露给全局
window.manageFeatureBlacklist = manageFeatureBlacklist;
window.showActivationCodes = showActivationCodes;
window.showChangePasswordModal = showChangePasswordModal;
window.logout = logout;
window.showTicketDetail = showTicketDetail;
window.showPayPerUseSection = showPayPerUseSection;
window.showPayPerUseRedeemTab = showPayPerUseRedeemTab;
window.viewPayPerUseBalance = viewPayPerUseBalance;
window.usePayPerUseCode = usePayPerUseCode;
