// 群聊列表模块
// 依赖：需要全局变量 currentAdminPassword, showToast, showModal, showLoading, hideLoading

// DOM 元素 (模块内管理)
let groupsTab;
let groupsContent;
let groupsTableBody;
let groupsLoadingMessage;
let groupsTable;
let noGroupsMessage;
let refreshAllGroupsButton;
let broadcastButton;
let selectAllGroups;
let botGroupStats;
let botStatsContent;
let totalGroupsCount;

// 广播模态框相关元素
let broadcastModal;
let broadcastMessage;
let confirmBroadcast;
let cancelBroadcast;

// 全局变量
let selectedGroupIds = new Set();

// 初始化群聊列表模块
function initGroupsModule() {
    // 获取DOM元素
    groupsTab = document.getElementById('groupsTab');
    groupsContent = document.getElementById('groupsContent');
    groupsTableBody = document.getElementById('groups-table-body');
    groupsLoadingMessage = document.getElementById('groupsLoadingMessage');
    groupsTable = document.getElementById('groupsTable');
    noGroupsMessage = document.getElementById('noGroupsMessage');
    refreshAllGroupsButton = document.getElementById('refreshAllGroupsButton');
    broadcastButton = document.getElementById('broadcastButton');
    selectAllGroups = document.getElementById('selectAllGroups');
    botGroupStats = document.getElementById('botGroupStats');
    botStatsContent = document.getElementById('botStatsContent');
    totalGroupsCount = document.getElementById('totalGroupsCount');

    // 广播模态框相关元素
    broadcastModal = document.getElementById('broadcastModal');
    broadcastMessage = document.getElementById('broadcastMessage');
    confirmBroadcast = document.getElementById('confirmBroadcast');
    cancelBroadcast = document.getElementById('cancelBroadcast');

    // 绑定事件监听器
    if (groupsTab) {
        groupsTab.addEventListener('click', () => {
            // 调用全局的switchTab函数
            if (window.switchTab) {
                window.switchTab('groups');
            }
        });
    }
    if (refreshAllGroupsButton) {
        refreshAllGroupsButton.addEventListener('click', fetchAllGroups);
    }
    if (broadcastButton) {
        broadcastButton.addEventListener('click', showBroadcastModal);
    }
    if (confirmBroadcast) {
        confirmBroadcast.addEventListener('click', sendBroadcast);
    }
    if (cancelBroadcast) {
        cancelBroadcast.addEventListener('click', () => {
            broadcastModal.classList.add('hidden');
            broadcastMessage.value = '';
        });
    }

    // 群聊全选功能
    if (selectAllGroups) {
        selectAllGroups.addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('.group-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                const groupId = checkbox.getAttribute('data-group-id');
                if (e.target.checked) {
                    selectedGroupIds.add(groupId);
                } else {
                    selectedGroupIds.delete(groupId);
                }
            });
            updateGroupDeleteButton();
        });
    }

    // 点击模态框外部关闭
    if (broadcastModal) {
        window.addEventListener('click', (e) => {
            if (e.target === broadcastModal) {
                broadcastModal.classList.add('hidden');
            }
        });
    }
}

// 群聊选择功能
function updateGroupDeleteButton() {
    const selectedCount = selectedGroupIds.size;
    const broadcastInfo = document.getElementById('broadcastInfo');
    if (broadcastInfo) {
        broadcastInfo.textContent = `已选择 ${selectedCount} 个群聊`;
    }
}

// 填充群聊表格
function populateAllGroupsTable(groups) {
    if (!groupsTableBody) return;

    groupsTableBody.innerHTML = '';

    if (!groups || groups.length === 0) {
        if (noGroupsMessage) noGroupsMessage.classList.remove('hidden');
        if (groupsTable) groupsTable.classList.add('hidden');
        return;
    }

    if (noGroupsMessage) noGroupsMessage.classList.add('hidden');
    if (groupsTable) groupsTable.classList.remove('hidden');

    groups.forEach(group => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-4 py-3" data-label="选择">
                <input type="checkbox" class="group-checkbox rounded" data-group-id="${group.groupId}">
            </td>
            <td class="px-4 py-3 text-sm" data-label="群号">
                ${group.groupId}
            </td>
            <td class="px-4 py-3" data-label="群头像">
                <img src="https://q1.qlogo.cn/g?b=qq&nk=${group.groupId}&s=100"
                     alt="群头像"
                     class="group-avatar"
                     onerror="this.src='data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22%3E%3Crect width=%22100%22 height=%22100%22 fill=%22%23e0e0e0%22/%3E%3Ctext x=%2250%22 y=%2250%22 text-anchor=%22middle%22 dy=%22.3em%22 fill=%22%23666%22 font-family=%22sans-serif%22 font-size=%2240%22%3E?%3C/text%3E%3C/svg%3E'">
            </td>
            <td class="px-4 py-3 text-sm font-medium" data-label="群名称">
                ${group.groupName}
            </td>
            <td class="px-4 py-3 text-sm" data-label="成员数">
                ${group.memberCount}/${group.maxMemberCount}
            </td>
            <td class="px-4 py-3 text-sm" data-label="备注">
                ${group.groupRemark || '-'}
            </td>
            <td class="px-4 py-3" data-label="状态">
                ${group.isAuthorized ?
                    '<span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">已授权</span>' :
                    '<span class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">未授权</span>'}
            </td>
            <td class="px-4 py-3 text-sm" data-label="所属BOT账号">
                ${group.botAccounts.map((account, idx) =>
                    `<span class="text-blue-600" title="${group.botNames[idx]}">${account}</span>`
                ).join(', ')}
            </td>
            <td class="px-4 py-3 text-center" data-label="操作">
                <button onclick="if (window.showLeaveGroupModal) window.showLeaveGroupModal('${group.groupId}', '${group.groupName}', ${JSON.stringify(group.robotIds).replace(/"/g, '&quot;')})"
                        class="text-red-600 hover:text-red-900">
                    退群
                </button>
            </td>
        `;
        groupsTableBody.appendChild(row);
    });

    // 重新绑定复选框事件
    document.querySelectorAll('.group-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const groupId = e.target.getAttribute('data-group-id');
            if (e.target.checked) {
                selectedGroupIds.add(groupId);
            } else {
                selectedGroupIds.delete(groupId);
            }
            updateGroupDeleteButton();
            updateSelectAllGroupsCheckbox();
        });
    });
}

// 更新全选复选框状态
function updateSelectAllGroupsCheckbox() {
    if (!selectAllGroups) return;

    const checkboxes = document.querySelectorAll('.group-checkbox');
    const checkedCount = document.querySelectorAll('.group-checkbox:checked').length;

    if (checkboxes.length === 0) {
        selectAllGroups.checked = false;
        selectAllGroups.disabled = true;
    } else {
        selectAllGroups.disabled = false;
        selectAllGroups.checked = checkedCount === checkboxes.length;
    }
}

// 获取所有群聊
async function fetchAllGroups() {
    if (!groupsLoadingMessage || !groupsTableBody) return;

    groupsLoadingMessage.classList.remove('hidden');
    groupsTableBody.innerHTML = '';
    selectedGroupIds.clear();

    try {
        const response = await fetch('/admin/groups/all', {
            method: 'GET',
            headers: {
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success && result.data) {
            // 更新统计信息
            if (result.data.botStats && result.data.botStats.length > 0 && botGroupStats && botStatsContent) {
                botGroupStats.classList.remove('hidden');
                botStatsContent.innerHTML = result.data.botStats.map(bot =>
                    `<div class="bg-white p-2 rounded shadow-sm">
                        <div class="font-medium text-gray-700">${bot.botName}</div>
                        <div class="text-sm text-gray-500">${bot.botAccount}</div>
                        <div class="text-lg font-bold text-blue-600">${bot.groupCount} 个群</div>
                    </div>`
                ).join('');
                if (totalGroupsCount) {
                    totalGroupsCount.textContent = result.data.totalGroups;
                }
            }

            populateAllGroupsTable(result.data.groups);
        }
    } catch (error) {
        console.error('获取群聊列表时出错:', error);
        if (typeof showToast === 'function') {
            showToast(`错误: ${error.message}`, 5000);
        }
    } finally {
        groupsLoadingMessage.classList.add('hidden');
    }
}

// 广播功能
function showBroadcastModal() {
    if (selectedGroupIds.size === 0) {
        if (typeof showToast === 'function') {
            showToast('请先选择要广播的群聊', 3000);
        }
        return;
    }

    if (broadcastModal) {
        broadcastModal.classList.remove('hidden');
        if (broadcastMessage) {
            broadcastMessage.value = '';
            broadcastMessage.focus();
        }
    }
}

async function sendBroadcast() {
    if (!broadcastMessage) return;

    const message = broadcastMessage.value.trim();
    if (!message) {
        if (typeof showToast === 'function') {
            showToast('请输入广播内容', 3000);
        }
        return;
    }

    if (!confirmBroadcast) return;

    confirmBroadcast.disabled = true;
    confirmBroadcast.innerHTML = '<span class="admin-loader inline-block mr-2"></span>发送中...';

    try {
        const response = await fetch('/admin/groups/broadcast', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({
                message: message,
                groupIds: Array.from(selectedGroupIds)
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            if (typeof showToast === 'function') {
                showToast(result.message, 5000);
            }
            if (broadcastModal) {
                broadcastModal.classList.add('hidden');
            }

            // 显示失败详情
            if (result.data.failedGroups && result.data.failedGroups.length > 0) {
                console.error('部分群聊发送失败:', result.data.failedGroups);
            }
        }
    } catch (error) {
        console.error('广播消息时出错:', error);
        if (typeof showToast === 'function') {
            showToast(`错误: ${error.message}`, 5000);
        }
    } finally {
        if (confirmBroadcast) {
            confirmBroadcast.disabled = false;
            confirmBroadcast.innerHTML = '发送';
        }
    }
}

// 暴露给全局
window.GroupsModule = {
    init: initGroupsModule,
    fetchAllGroups: fetchAllGroups,
    populateAllGroupsTable: populateAllGroupsTable,
    showBroadcastModal: showBroadcastModal,
    sendBroadcast: sendBroadcast,
    updateGroupDeleteButton: updateGroupDeleteButton,
    updateSelectAllGroupsCheckbox: updateSelectAllGroupsCheckbox,
    selectedGroupIds: selectedGroupIds
};

// 导出模块初始化函数
window.initGroupsModule = initGroupsModule;
