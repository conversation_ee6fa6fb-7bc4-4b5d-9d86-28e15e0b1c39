// 绑定管理模块
// 依赖：需要全局变量 tiersConfig, currentAdminPassword, showToast, showModal, showLoading, hideLoading

// DOM 元素 (这些变量在admin.js中定义，全局可用)
let bindingsTableBody;
let loadingMessage;
let noRecordsMessage;
let bindingsTable;
let bindingsSearchInput;
let bindingStatusFilter;
let bindingTierFilter;
let refreshBindingsButton;
let clearBindingsFiltersButton;
let bindingModal;
let bindingModalTitle;
let bindingForm;
let closeBindingModal;
let cancelBindingButton;
let saveBindingButton;
let bindingModalError;
let addBindingButton;
let checkExpirationsButton;

let originalBindingsData = [];

// 初始化绑定管理模块
function initBindingsModule() {
    // 获取DOM元素 (bindingsContent已在admin.js中定义)
    bindingsTableBody = document.getElementById('bindings-table-body');
    loadingMessage = document.getElementById('loadingMessage');
    noRecordsMessage = document.getElementById('noRecordsMessage');
    bindingsTable = document.getElementById('bindingsTable');
    bindingsSearchInput = document.getElementById('bindingsSearchInput');
    bindingStatusFilter = document.getElementById('bindingStatusFilter');
    bindingTierFilter = document.getElementById('bindingTierFilter');
    refreshBindingsButton = document.getElementById('refreshBindingsButton');
    clearBindingsFiltersButton = document.getElementById('clearBindingsFiltersButton');
    bindingModal = document.getElementById('bindingModal');
    bindingModalTitle = document.getElementById('bindingModalTitle');
    bindingForm = document.getElementById('bindingForm');
    closeBindingModal = document.getElementById('closeBindingModal');
    cancelBindingButton = document.getElementById('cancelBindingButton');
    saveBindingButton = document.getElementById('saveBindingButton');
    bindingModalError = document.getElementById('bindingModalError');
    addBindingButton = document.getElementById('addBindingButton');
    checkExpirationsButton = document.getElementById('checkExpirationsButton');

    // 绑定事件监听器
    if (refreshBindingsButton) {
        refreshBindingsButton.addEventListener('click', fetchBindings);
    }
    if (clearBindingsFiltersButton) {
        clearBindingsFiltersButton.addEventListener('click', clearBindingsFilters);
    }
    if (bindingsSearchInput) {
        bindingsSearchInput.addEventListener('input', applyBindingsFilters);
    }
    if (bindingStatusFilter) {
        bindingStatusFilter.addEventListener('change', applyBindingsFilters);
    }
    if (bindingTierFilter) {
        bindingTierFilter.addEventListener('change', applyBindingsFilters);
    }
    if (addBindingButton) {
        addBindingButton.addEventListener('click', openAddBindingModal);
    }
    if (checkExpirationsButton) {
        checkExpirationsButton.addEventListener('click', () => manualCheckExpirations(true));
    }
    if (bindingForm) {
        bindingForm.addEventListener('submit', saveBinding);
    }
    if (closeBindingModal) {
        closeBindingModal.addEventListener('click', () => bindingModal.classList.add('hidden'));
    }
    if (cancelBindingButton) {
        cancelBindingButton.addEventListener('click', () => bindingModal.classList.add('hidden'));
    }

    // 永久到期时间复选框事件
    const permanentCheckbox = document.getElementById('modalPermanentExpiration');
    const expirationInput = document.getElementById('modalExpirationTimestamp');
    if (permanentCheckbox && expirationInput) {
        permanentCheckbox.addEventListener('change', function () {
            expirationInput.disabled = this.checked;
            if (this.checked) {
                expirationInput.value = '';
            }
        });
    }
}

// 获取绑定列表
async function fetchBindings() {
    loadingMessage.classList.remove('hidden');
    noRecordsMessage.classList.add('hidden');
    bindingsTable.classList.add('hidden');
    bindingsTableBody.innerHTML = '';

    try {
        const response = await fetch('/admin/bindings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            }
        });

        if (!response.ok) {
            const errorResult = await response.json().catch(() => ({ message: '获取绑定列表失败' }));
            throw new Error(errorResult.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            // 存储原始数据
            originalBindingsData = result.data || [];
            // 填充档位过滤器选项
            populateBindingTierFilter();
            // 应用当前的搜索和过滤条件
            applyBindingsFilters();
        } else {
            throw new Error(result.message || '获取到的数据格式不正确');
        }
    } catch (error) {
        console.error('获取绑定列表时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    } finally {
        loadingMessage.classList.add('hidden');
    }
}

// 填充档位过滤器选项
function populateBindingTierFilter() {
    if (!bindingTierFilter) return;

    // 获取所有唯一的档位
    const uniqueTiers = [...new Set(originalBindingsData.map(binding => binding.skuId))];

    // 清空现有选项（保留"全部档位"选项）
    bindingTierFilter.innerHTML = '<option value="">全部档位</option>';

    // 添加档位选项
    uniqueTiers.forEach(skuId => {
        const tierName = getTierDisplayName(skuId);
        const option = document.createElement('option');
        option.value = skuId;
        option.textContent = tierName;
        bindingTierFilter.appendChild(option);
    });
}

// 应用搜索和过滤条件
function applyBindingsFilters() {
    const searchText = bindingsSearchInput ? bindingsSearchInput.value.toLowerCase().trim() : '';
    const statusFilter = bindingStatusFilter ? bindingStatusFilter.value : '';
    const tierFilter = bindingTierFilter ? bindingTierFilter.value : '';

    let filteredData = [...originalBindingsData];

    // 应用搜索过滤
    if (searchText) {
        filteredData = filteredData.filter(binding => {
            const searchFields = [
                binding.groupNumber || '',
                binding.owner || '',
                binding.ownerQQ || '',
                getTierDisplayName(binding.skuId),
                ...(binding.allCodes || [binding.orderNumber])
            ].map(field => String(field).toLowerCase());

            return searchFields.some(field => field.includes(searchText));
        });
    }

    // 应用状态过滤
    if (statusFilter) {
        filteredData = filteredData.filter(binding => {
            if (statusFilter === 'expired') {
                // 已过期：明确有到期时间且已经过期（ttl为-2或通过时间判断确实过期）
                if (binding.ttl === -2) return true; // 后端明确标记为已过期
                if (binding.ttl === -1 || binding.ttl === null) return false; // 永久绑定不算过期
                if (binding.expirationISOString && binding.expirationDate !== '永久') {
                    return new Date(binding.expirationISOString).getTime() < Date.now();
                }
                return false;
            } else if (statusFilter === 'active') {
                // 有效：永久绑定或未过期的时间绑定
                if (binding.ttl === -1 || binding.ttl === null) return true; // 永久绑定
                if (binding.ttl === -2) return false; // 明确已过期
                if (binding.expirationDate === '永久') return true; // 显示为永久
                if (binding.expirationISOString) {
                    return new Date(binding.expirationISOString).getTime() >= Date.now();
                }
                return true; // 默认认为有效
            }
            return true;
        });
    }

    // 应用档位过滤
    if (tierFilter) {
        filteredData = filteredData.filter(binding => binding.skuId === tierFilter);
    }

    // 显示过滤后的结果
    populateBindingsTable({ success: true, data: filteredData });
}

// 清空所有过滤条件
function clearBindingsFilters() {
    if (bindingsSearchInput) bindingsSearchInput.value = '';
    if (bindingStatusFilter) bindingStatusFilter.value = '';
    if (bindingTierFilter) bindingTierFilter.value = '';
    applyBindingsFilters();
}

// 填充绑定表格
function populateBindingsTable(data) {
    bindingsTableBody.innerHTML = '';

    // 检查数据结构
    const bindings = data.data || data;

    if (!bindings || bindings.length === 0) {
        noRecordsMessage.classList.remove('hidden');
        bindingsTable.classList.add('hidden');
        return;
    }

    noRecordsMessage.classList.add('hidden');
    bindingsTable.classList.remove('hidden');

    bindings.forEach((binding, index) => {
        const row = document.createElement('tr');
        // 过期判断：优先使用后端 ttl（-2 为已过期）；回退使用 expirationISOString 与当前时间比较
        const isExpired = (typeof binding.ttl === 'number' && binding.ttl === -2) ||
            (!!binding.expirationISOString && new Date(binding.expirationISOString).getTime() < Date.now());

        // 计算剩余天数
        let remainingDaysHtml = '';
        if (binding.expirationDate && binding.expirationDate !== '永久' && binding.expirationISOString) {
            const expirationTime = new Date(binding.expirationISOString);
            const now = new Date();
            const diffTime = expirationTime.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays > 0) {
                if (diffDays <= 7) {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-orange-100 text-orange-700">剩余${diffDays}天</span>`;
                } else if (diffDays <= 30) {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-yellow-100 text-yellow-700">剩余${diffDays}天</span>`;
                } else {
                    remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-700">剩余${diffDays}天</span>`;
                }
            } else if (diffDays === 0) {
                remainingDaysHtml = ` <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-700">今日到期</span>`;
            }
        }

        let expirationHtml = `${binding.expirationDate}${remainingDaysHtml}`;
        if (isExpired) {
            expirationHtml += ' <span class="ml-1 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-700">已过期</span>';
        }

        row.innerHTML = `
            <td class="px-4 py-3 text-sm" data-label="序号">${index + 1}</td>
            <td class="px-4 py-3 text-sm" data-label="群号">${binding.groupNumber}</td>
            <td class="px-4 py-3" data-label="群头像">
                <img src="https://q1.qlogo.cn/g?b=qq&nk=${binding.groupNumber}&s=100"
                     alt="群头像"
                     class="group-avatar"
                     onerror="this.src='data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22%3E%3Crect width=%22100%22 height=%22100%22 fill=%22%23e0e0e0%22/%3E%3Ctext x=%2250%22 y=%2250%22 text-anchor=%22middle%22 dy=%22.3em%22 fill=%22%23666%22 font-family=%22sans-serif%22 font-size=%2240%22%3E?%3C/text%3E%3C/svg%3E'">
            </td>
            <td class="px-4 py-3 text-sm" data-label="激活码">
                <span class="activation-code-link cursor-pointer"
                      onclick="showGroupActivationCodes('${binding.groupNumber}', ${JSON.stringify(binding.allCodes).replace(/"/g, '&quot;')})">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    查看激活码
                </span>
            </td>
            <td class="px-4 py-3 text-sm" data-label="档位">
                <span class="tier-badge unified">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm4 3a1 1 0 100-2 1 1 0 000 2zm5-1a1 1 0 11-2 0 1 1 0 012 0z" clip-rule="evenodd"></path>
                    </svg>
                    ${getTierDisplayName(binding.skuId)}
                </span>
            </td>
            <td class="px-4 py-3 text-sm" data-label="所属用户">${binding.owner || 'admin'}</td>
            <td class="px-4 py-3 text-sm" data-label="用户QQ">${binding.ownerQQ || '未绑定'}</td>
            <td class="px-4 py-3 text-sm" data-label="到期时间">${expirationHtml}</td>
            <td class="px-4 py-3 text-sm text-center" data-label="操作">
                <div class="action-buttons flex justify-center gap-2">
                    <button onclick="openEditBindingModal('${binding.orderNumber}', '${binding.skuId}', '${binding.groupNumber}', '${binding.expirationISOString || ''}', '${binding.owner || 'admin'}')"
                            class="text-blue-600 hover:text-blue-900">编辑</button>
                    <button onclick="deleteBinding('${binding.orderNumber}', '${binding.skuId}')"
                            class="text-red-600 hover:text-red-900">删除</button>
                    <button onclick="sendExpiryReminder('${binding.groupNumber}', '${binding.skuId}', '${binding.owner || 'admin'}')"
                            class="text-emerald-700 hover:text-emerald-900">发送到期提醒</button>
                </div>
            </td>
        `;
        if (isExpired) {
            row.classList.add('bg-red-50');
            row.setAttribute('data-expired', '1');
        }
        bindingsTableBody.appendChild(row);
    });
}

// 打开编辑绑定模态框
function openEditBindingModal(orderNumber, skuId, groupNumber, expirationISOString, owner) {
    bindingModalTitle.textContent = '编辑绑定';
    document.getElementById('originalOrderNumber').value = orderNumber;
    document.getElementById('originalSkuId').value = skuId;
    document.getElementById('modalOrderNumber').value = orderNumber;
    document.getElementById('modalGroupNumber').value = groupNumber;
    document.getElementById('modalSkuId').value = skuId;

    // 设置到期时间
    const permanentCheckbox = document.getElementById('modalPermanentExpiration');
    const expirationInput = document.getElementById('modalExpirationTimestamp');

    if (!expirationISOString || expirationISOString === 'null') {
        permanentCheckbox.checked = true;
        expirationInput.disabled = true;
        expirationInput.value = '';
    } else {
        permanentCheckbox.checked = false;
        expirationInput.disabled = false;
        expirationInput.value = expirationISOString;
    }

    bindingModal.classList.remove('hidden');
}

// 删除绑定
async function deleteBinding(orderNumber, skuId) {
    if (!confirm('确定要删除这个绑定吗？')) {
        return;
    }

    try {
        const response = await fetch('/admin/bindings/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ orderNumber, skuId })
        });

        if (!response.ok) {
            const errorResult = await response.json().catch(() => ({ message: '删除失败' }));
            throw new Error(errorResult.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast('删除成功');
            fetchBindings();
        } else {
            throw new Error(result.message || '删除失败');
        }
    } catch (error) {
        console.error('删除绑定时出错:', error);
        showToast(`错误: ${error.message}`, 5000);
    }
}

// 打开添加绑定模态框
function openAddBindingModal() {
    bindingModalTitle.textContent = '添加新绑定';
    bindingForm.reset();
    document.getElementById('originalOrderNumber').value = '';
    document.getElementById('originalSkuId').value = '';
    document.getElementById('modalPermanentExpiration').checked = false;
    document.getElementById('modalExpirationTimestamp').disabled = false;
    bindingModal.classList.remove('hidden');
}

// 手动发送到期提醒（单条绑定）
async function sendExpiryReminder(groupNumber, skuId, owner) {
    if (!confirm('确认向该绑定的用户发送到期提醒邮件？')) return;
    try {
        const resp = await fetch('/admin/bindings/send-expiry-reminder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ groupNumber, skuId, owner })
        });
        const result = await resp.json().catch(() => ({ success: false }));
        if (!resp.ok || !result.success) throw new Error(result.message || '发送失败');
        showToast('到期提醒已发送', 'success');
    } catch (e) {
        console.error('发送到期提醒失败:', e);
        showToast(e.message || '发送失败', 'error');
    }
}

// 手动检查即将到期（可触发群发提醒）
async function manualCheckExpirations(send = true) {
    try {
        const resp = await fetch('/admin/expirations/check', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify({ send })
        });
        const result = await resp.json().catch(() => ({ success: false }));
        if (!resp.ok || !result.success) throw new Error(result.message || '检查失败');
        const data = result.data || {};
        showToast(`检查完成：匹配 ${data.matched || 0}，已发送 ${data.sent || 0}`, 'success');
    } catch (e) {
        console.error('手动检查到期失败:', e);
        showToast(e.message || '检查失败', 'error');
    }
}

// 保存绑定
async function saveBinding(e) {
    e.preventDefault();

    const originalOrderNumber = document.getElementById('originalOrderNumber').value;
    const orderNumber = document.getElementById('modalOrderNumber').value.trim();
    const groupNumber = document.getElementById('modalGroupNumber').value.trim();
    const skuId = document.getElementById('modalSkuId').value;
    const isPermanent = document.getElementById('modalPermanentExpiration').checked;
    const expirationTimestamp = document.getElementById('modalExpirationTimestamp').value;

    if (!orderNumber || !groupNumber || !skuId) {
        bindingModalError.textContent = '请填写所有必填字段';
        return;
    }

    showLoading(saveBindingButton);
    bindingModalError.textContent = '';

    try {
        const isEdit = !!originalOrderNumber;
        const url = isEdit ? '/admin/bindings/update' : '/admin/bindings/add';

        const body = {
            orderNumber,
            groupNumber,
            skuId,
            isPermanent,
            expirationTimestamp: isPermanent ? null : expirationTimestamp
        };

        if (isEdit) {
            body.originalOrderNumber = originalOrderNumber;
            body.originalSkuId = document.getElementById('originalSkuId').value;
            body.newOrderNumber = orderNumber;
            body.newSkuId = skuId;
            body.newGroupNumber = groupNumber;
        }

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Admin-Password': currentAdminPassword
            },
            body: JSON.stringify(body)
        });

        if (!response.ok) {
            const errorResult = await response.json().catch(() => ({ message: '保存失败' }));
            throw new Error(errorResult.message || `HTTP error ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            showToast(isEdit ? '更新成功' : '添加成功');
            bindingModal.classList.add('hidden');
            fetchBindings();
        } else {
            throw new Error(result.message || '保存失败');
        }
    } catch (error) {
        console.error('保存绑定时出错:', error);
        bindingModalError.textContent = error.message;
    } finally {
        hideLoading(saveBindingButton);
    }
}

// 显示群组的所有激活码
function showGroupActivationCodes(groupNumber, allCodes) {
    const codesList = allCodes.map((code, index) => `
        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg mb-2 hover:from-blue-50 hover:to-blue-100 transition-all duration-200">
            <div class="flex items-center">
                <span class="flex items-center justify-center w-6 h-6 bg-blue-500 text-white text-xs font-bold rounded-full mr-3">${index + 1}</span>
                <code class="font-mono text-sm bg-white px-2 py-1 rounded border">${code}</code>
            </div>
            <button onclick="copyToClipboard('${code}')" class="text-blue-600 hover:text-blue-800 transition-colors duration-200" title="复制激活码">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
            </button>
        </div>
    `).join('');

    const modalContent = `
        <div class="max-h-96 overflow-y-auto">
            <div class="mb-4 p-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg">
                <h3 class="text-lg font-semibold flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    群号 ${groupNumber} 的激活码历史
                </h3>
                <p class="text-blue-100 text-sm mt-1">该群组累计使用了 ${allCodes.length} 个激活码</p>
            </div>
            <div class="space-y-2">
                ${codesList}
            </div>
        </div>
    `;

    showModal('群组激活码列表', modalContent);
}

// 复制到剪贴板的函数
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('激活码已复制到剪贴板', 2000);
    }).catch(err => {
        console.error('复制失败:', err);
        showToast('复制失败，请手动选择复制', 3000);
    });
}

// 获取档位显示名称
function getTierDisplayName(skuId) {
    if (window.TiersModule && window.TiersModule.getTiersConfig) {
        const tiersConfig = window.TiersModule.getTiersConfig();
        const tier = tiersConfig.find(t => t.sku_id === skuId);
        return tier ? tier.display_name : '未知档位';
    }
    return '未知档位';
}

// 暴露给全局
window.fetchBindings = fetchBindings;
window.openEditBindingModal = openEditBindingModal;
window.deleteBinding = deleteBinding;
window.sendExpiryReminder = sendExpiryReminder;
window.showGroupActivationCodes = showGroupActivationCodes;
window.copyToClipboard = copyToClipboard;
window.getTierDisplayName = getTierDisplayName;

// 导出模块初始化函数
window.initBindingsModule = initBindingsModule;
