<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理面板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
</head>

<body class="min-h-screen p-4 sm:p-6 md:p-8">

    <div class="container mx-auto max-w-7xl h-screen flex flex-col">
        <header class="mb-4 text-center flex-shrink-0">
            <h1 class="text-4xl font-bold text-slate-800">后台管理面板</h1>
        </header>

        <!-- 登录区域 -->
        <section id="password-section" class="bg-white p-6 sm:p-8 rounded-xl shadow-xl max-w-lg mx-auto slide-down">
            <form id="password-form" class="space-y-6">
                <div>
                    <label for="admin-password" class="block text-sm font-medium text-gray-700 mb-1">管理员密码</label>
                    <input type="password" id="admin-password" name="admin-password" required
                        autocomplete="current-password"
                        class="mt-1 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>
                <button type="submit" id="loginButton"
                    class="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform transform hover:scale-105">
                    <span class="button-text">进入管理后台</span>
                    <span class="admin-loader hidden ml-2"></span>
                </button>
                <div id="password-error" class="text-sm text-red-600 mt-2 min-h-[1.25rem]" aria-live="polite"></div>
            </form>
        </section>

        <!-- 主面板 -->
        <section id="main-panel" class="hidden bg-white p-6 sm:p-8 rounded-xl shadow-xl fade-in flex-1 flex flex-col min-h-0">
            <!-- Tab导航 -->
            <div class="border-b border-gray-200 mb-4 flex-shrink-0">
                <nav class="-mb-px flex space-x-8 overflow-x-auto" aria-label="Tabs">
                    <button id="bindingsTab" class="tab-button active">
                        绑定管理
                    </button>
                    <button id="codesTab" class="tab-button">
                        兑换码管理
                    </button>
                    <button id="robotsTab" class="tab-button">
                        机器人列表
                    </button>
                    <button id="groupsTab" class="tab-button">
                        群聊列表
                    </button>
                    <button id="usersTab" class="tab-button" onclick="onUsersTabClick()">
                        用户管理
                    </button>
                    <button id="featureBlacklistTab" class="tab-button" onclick="switchTab('feature-blacklist'); onFeatureBlacklistTabClick()">
                        功能黑名单
                    </button>
                    <button id="featureWhitelistTab" class="tab-button" onclick="switchTab('feature-whitelist'); onFeatureWhitelistTabClick()">
                        功能白名单
                    </button>
                    <button id="ticketsTab" class="tab-button">
                        工单管理
                    </button>
                    <button id="ticketTypesTab" class="tab-button">
                        工单类型
                    </button>
                    <button id="tiersTab" class="tab-button" onclick="switchTab('tiers')">
                        档位管理
                    </button>
                    <button id="systemLogsTab" class="tab-button" onclick="switchTab('system-logs')">
                        系统日志
                    </button>
                    <button id="settingsTab" class="tab-button">
                        系统设置
                    </button>
                    <button id="payPerUseTab" class="tab-button">
                        按次付费
                    </button>
                </nav>
            </div>

            <!-- 绑定管理Tab内容 -->
            <div id="bindingsContent" class="tab-content flex-1 flex flex-col min-h-0">
                <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
                    <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">绑定列表</h2>
                    <div class="flex gap-2">
                    <button id="addBindingButton"
                        class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                                clip-rule="evenodd" />
                        </svg>
                        添加新绑定
                    </button>
                    <button id="checkExpirationsButton"
                        class="flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-0.5">
                        <svg xmlns="http://www/w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.5 2.5a1 1 0 101.414-1.414L11 9.586V6z" clip-rule="evenodd" />
                        </svg>
                        检查到期并发送
                    </button>
                    </div>
                </div>

                <!-- 搜索过滤器区域 -->
                <div class="mb-4 p-3 bg-gray-50 rounded-lg flex-shrink-0">
                    <div class="flex flex-wrap gap-3 items-center">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <input type="text" id="bindingsSearchInput" placeholder="搜索群号、群名称、所属用户、用户QQ..." 
                                class="px-3 py-2 border border-gray-300 rounded-md w-80 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <select id="bindingStatusFilter" class="px-3 py-2 border border-gray-300 rounded-md">
                            <option value="">全部状态</option>
                            <option value="active">有效</option>
                            <option value="expired">已过期</option>
                        </select>
                        <select id="bindingTierFilter" class="px-3 py-2 border border-gray-300 rounded-md">
                            <option value="">全部档位</option>
                        </select>
                        <button id="refreshBindingsButton" 
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            刷新
                        </button>
                        <button id="clearBindingsFiltersButton" 
                            class="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            清空筛选
                        </button>
                    </div>
                </div>

                <div id="loadingMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
                    <div class="admin-loader inline-block mr-2"></div> 加载中，请稍候...
                </div>
                <div id="noRecordsMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-2" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    当前没有绑定记录。
                </div>
<!-- 在绑定管理Tab内容中的表格部分 -->
                <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
                    <table id="bindingsTable" class="admin-table-container responsive-table hidden">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col"
                                    class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    序号</th>
                                <th scope="col"
                                    class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    群号</th>
                                <th scope="col"
                                    class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    群头像</th>
                                <th scope="col"
                                    class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    激活码</th>
                                <th scope="col"
                                    class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    档位</th>
                                <th scope="col"
                                    class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                     所属用户</th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                     用户QQ</th>
                                <th scope="col"
                                    class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    到期时间</th>
                                <th scope="col"
                                    class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    操作</th>
                            </tr>
                        </thead>
                        <tbody id="bindings-table-body" class="bg-white divide-y divide-gray-200">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 兑换码管理Tab内容 -->
            <div id="codesContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
                <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
                    <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">兑换码管理</h2>
                    <div class="flex gap-2">
                        <button id="createCodesButton"
                            class="flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            批量创建
                        </button>
                        <button id="exportCodesButton"
                            class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            导出
                        </button>
                        <button id="exportSelectedCodesButton"
                            class="flex items-center justify-center bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 disabled:opacity-50" disabled>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                            导出选中
                        </button>
                    </div>
                </div>

                <!-- 过滤器 -->
                <div class="mb-4 p-3 bg-gray-50 rounded-lg flex-shrink-0">
                    <div class="flex flex-wrap gap-3 items-center">
                        <select id="codeStatusFilter" class="px-3 py-2 border border-gray-300 rounded-md">
                            <option value="">全部状态</option>
                            <option value="0">未使用</option>
                            <option value="1">已使用</option>
                        </select>
                        <input type="text" id="codeSearchInput" placeholder="搜索使用者/备注/创建时间" 
                            class="px-3 py-2 border border-gray-300 rounded-md w-64">
                        <select id="codesPerPage" class="px-3 py-2 border border-gray-300 rounded-md">
                            <option value="20">20个/页</option>
                            <option value="50">50个/页</option>
                            <option value="100">100个/页</option>
                        </select>
                        <button id="refreshCodesButton" 
                            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md">
                            刷新
                        </button>
                    </div>
                </div>
                <div id="codesLoadingMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
                    <div class="admin-loader inline-block mr-2"></div> 加载中，请稍候...
                </div>
                <!-- 表格部分，删除批次ID列 -->
                <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
                    <table id="codesTable" class="admin-table-container responsive-table">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" id="selectAllCodes" class="rounded">
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    兑换码
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    档位
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    时长
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    状态
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    使用者
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    备注
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    创建时间
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody id="codes-table-body" class="bg-white divide-y divide-gray-200">
                        </tbody>
                    </table>
                </div>

                <!-- 批量操作 -->
                <div class="mt-3 flex justify-between items-center flex-shrink-0">
                    <div class="flex gap-2">
                        <button id="deleteSelectedCodesButton" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md disabled:opacity-50" disabled>
                            删除选中
                        </button>
                        <span id="selectedCodesCount" class="text-sm text-gray-600 ml-2"></span>
                    </div>
                <!-- 分页 -->
                <div id="codesPagination" class="flex justify-center gap-2">
                    <!-- 分页控件将在这里动态添加 -->
                </div>
            </div>
        </div>

            <!-- 机器人列表Tab内容 -->
            <div id="robotsContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
                <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
                    <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">机器人管理</h2>
                    <button id="addRobotButton"
                        class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        添加机器人
                    </button>
                </div>

                <div id="robotsLoadingMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
                    <div class="admin-loader inline-block mr-2"></div> 加载中，请稍候...
                </div>

                <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
                    <table id="robotsTable" class="admin-table-container responsive-table">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    机器人账号
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    机器人名称
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    API地址
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    Token
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    状态
                                </th>
                                <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody id="robots-table-body" class="bg-white divide-y divide-gray-200">
                        </tbody>
                    </table>
                </div>
            </div>
<!-- 群聊列表Tab内容 -->
<div id="groupsContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
        <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">群聊列表</h2>
        <div class="flex gap-2">
            <button id="broadcastButton"
                class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                </svg>
                群聊广播
            </button>
            <button id="refreshAllGroupsButton"
                class="flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                刷新所有机器人群聊
            </button>
        </div>
    </div>
    <div id="groupsLoadingMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
        <div class="admin-loader inline-block mr-2"></div> 加载中，请稍候...
    </div>
    <!-- 机器人群聊统计 -->
    <div id="botGroupStats" class="mb-4 p-4 bg-blue-50 rounded-lg hidden">
        <h3 class="text-sm font-semibold text-gray-700 mb-2">机器人群聊统计：</h3>
        <div id="botStatsContent" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 text-sm">
            <!-- 统计内容将在这里动态生成 -->
        </div>
        <div class="mt-2 pt-2 border-t border-blue-200">
            <span class="text-sm font-medium text-gray-700">总群聊数：<span id="totalGroupsCount" class="font-bold text-blue-600">0</span></span>
        </div>
    </div>
    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
        <table id="groupsTable" class="admin-table-container responsive-table hidden">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" id="selectAllGroups" class="rounded">
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        群号
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        群头像
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        群名称
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        成员数
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        备注
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        状态
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        所属BOT账号
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-center text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        操作
                    </th>
                </tr>
            </thead>
            <tbody id="groups-table-body" class="bg-white divide-y divide-gray-200">
            </tbody>
        </table>
        <div id="noGroupsMessage" class="text-center py-10 text-gray-500 flex-shrink-0">
            点击"刷新所有机器人群聊"按钮获取群聊列表
        </div>
    </div>
</div>
<!-- 档位管理Tab内容 -->
<div id="tiersContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
        <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">档位管理</h2>
        <div class="flex gap-2">
            <button id="addTierButton" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                添加档位
            </button>

        </div>
    </div>

    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
        <table id="tiersTable" class="admin-table-container responsive-table">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">SKU ID</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">名称</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">价格</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">描述</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody id="tiersTableBody" class="bg-white divide-y divide-gray-200">
            </tbody>
        </table>
    </div>
</div>

<!-- 档位编辑模态框 -->
<div id="tierModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 id="tierModalTitle" class="text-lg font-medium text-gray-900">添加档位</h3>
            <button id="closeTierModal" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="tierForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">SKU ID *</label>
                <input type="text" id="tierSkuId" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
                <p class="mt-1 text-xs text-gray-500">唯一标识；创建后不建议修改，如需修改可直接编辑后保存</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">显示名称 *</label>
                <input type="text" id="tierDisplayName" required class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">价格</label>
                <input type="number" id="tierPrice" min="0" step="0.01" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                <textarea id="tierDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md resize-none"></textarea>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">功能列表（可选）</label>
                <input type="text" id="tierFeatures" placeholder="用逗号分隔，例如：基础功能,高级支持" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div class="flex justify-end space-x-2 pt-2">
                <button type="button" id="cancelTierButton" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">取消</button>
                <button type="submit" id="saveTierButton" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">保存</button>
            </div>
        </form>
    </div>
 </div>
            <!-- 系统日志Tab内容 -->
            <div id="systemLogsContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
                <div class="flex items-center justify-between mb-4 flex-shrink-0">
                    <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">系统日志</h2>
                    <div class="flex items-center gap-2">
                        <select id="systemLogsDays" class="px-3 py-2 border rounded-md">
                            <option value="1">最近1天</option>
                            <option value="3">最近3天</option>
                            <option value="7" selected>最近7天</option>
                            <option value="14">最近14天</option>
                        </select>
                        <button id="refreshSystemLogsButton" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md">刷新</button>
                    </div>
                </div>
                <div id="systemLogsContainer" class="space-y-4 overflow-y-auto min-h-0 flex-1"></div>
            </div>

            <!-- 系统设置Tab内容 -->
            <div id="settingsContent" class="tab-content hidden">
                <div class="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
                    <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">系统设置</h2>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-gray-50 p-4 rounded-lg border">
                        <h3 class="font-medium text-gray-800 mb-3">邮件与管理员信息</h3>
                        <div class="space-y-3">
                            <label class="block text-sm">管理员邮箱
                                <input id="settingAdminEmail" type="email" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="<EMAIL>">
                            </label>
                            <label class="block text-sm">SMTP Host
                                <input id="settingSmtpHost" type="text" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="smtp.example.com">
                            </label>
                            <div class="grid grid-cols-2 gap-3">
                                <label class="block text-sm">SMTP Port
                                    <input id="settingSmtpPort" type="number" class="mt-1 w-full px-3 py-2 border rounded-md" value="587">
                                </label>
                                <label class="block text-sm">TLS
                                    <select id="settingSmtpTLS" class="mt-1 w-full px-3 py-2 border rounded-md">
                                        <option value="1">启用</option>
                                        <option value="0">关闭</option>
                                    </select>
                                </label>
                            </div>
                            <label class="block text-sm">SMTP 用户名
                                <input id="settingSmtpUser" type="text" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="<EMAIL>">
                            </label>
                            <label class="block text-sm">SMTP 密码
                                <input id="settingSmtpPass" type="password" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="******">
                            </label>
                            <label class="block text-sm">发件邮箱(From)
                                <input id="settingSmtpFrom" type="email" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="<EMAIL>">
                            </label>
                            <label class="block text-sm">发件人名称
                                <input id="settingSmtpFromName" type="text" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="例如：<EMAIL> 或 站点名称">
                            </label>
                            <label class="block text-sm">网站名称
                                <input id="settingSiteTitle" type="text" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="例如：小阡自助系统">
                            </label>
                            <label class="block text-sm">客服联系方式
                                <input id="settingSupportContact" type="text" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="例如：************ 或 QQ/微信等">
                            </label>
                            <label class="block text-sm">网站链接
                                <input id="settingSiteLink" type="url" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="例如：https://example.com">
                            </label>
                            <label class="block text-sm">背景图API地址
                                <input id="settingBackgroundImageApi" type="url" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="例如：https://t.alcy.cc/ycy">
                                <p class="text-xs text-gray-500 mt-1">留空则使用默认背景图</p>
                            </label>
                            <label class="block text-sm">允许的邮箱后缀（逗号或空格分隔，留空不限制）
                                <input id="settingAllowedEmailSuffixes" type="text" class="mt-1 w-full px-3 py-2 border rounded-md" placeholder="例如：qq.com, 163.com 或 @corp.com">
                            </label>
                            <div class="flex gap-3">
                                <button id="saveSettingsButton" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">保存设置</button>
                                <button id="testEmailButton" class="px-4 py-2 bg-gray-700 hover:bg-gray-800 text-white rounded-md">发送测试邮件</button>
                            </div>
                            <div id="settingsSaveMsg" class="text-sm text-gray-600"></div>
                        </div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg border">
                        <h3 class="font-medium text-gray-800 mb-3">功能开关</h3>
                        <div class="space-y-3">
                            <label class="flex items-center gap-2">
                                <input id="toggleTicketsEnabled" type="checkbox" class="h-4 w-4">
                                <span>启用工单系统</span>
                            </label>
                            <label class="flex items-center gap-2">
                                <input id="toggleFeatureBlacklistEnabled" type="checkbox" class="h-4 w-4">
                                <span>启用功能黑名单</span>
                            </label>
                            <label class="flex items-center gap-2">
                                <input id="toggleFeatureWhitelistEnabled" type="checkbox" class="h-4 w-4">
                                <span>启用功能白名单</span>
                            </label>
                            <label class="flex items-center gap-2">
                                <input id="togglePayPerUseEnabled" type="checkbox" class="h-4 w-4">
                                <span>启用按次付费功能</span>
                            </label>
                            <div class="space-y-2">
                                <div class="flex items-center gap-2">
                                    <input id="toggleRobotGroupsAutoRefreshEnabled" type="checkbox" class="h-4 w-4">
                                    <span>启用机器人群聊自动刷新</span>
                                </div>
                                <div class="flex items-center gap-2 ml-6">
                                    <label class="block text-sm">刷新间隔</label>
                                    <input id="inputRobotGroupsRefreshInterval" type="number" min="5" value="60" class="w-24 px-2 py-1 border rounded-md" placeholder="分钟">
                                    <span class="text-sm text-gray-600">分钟（最小5分钟）</span>
                                </div>
                            </div>
                             <label class="flex items-center gap-2">
                                 <input id="toggleRegisterEmailVerificationEnabled" type="checkbox" class="h-4 w-4">
                                 <span>注册需邮箱验证（默认关闭）</span>
                             </label>
                             <label class="flex items-center gap-2">
                                 <input id="toggleTicketEmailNotifyEnabled" type="checkbox" class="h-4 w-4">
                                 <span>启用工单邮件提醒（不高于用户个人开关）</span>
                             </label>
                             <label class="flex items-center gap-2">
                                 <input id="toggleAdminTicketEmailNotifyEnabled" type="checkbox" class="h-4 w-4">
                                 <span>有新工单/等待回复时提醒管理员邮箱</span>
                             </label>
                             <label class="flex items-center gap-2">
                                 <input id="toggleAdminReplyEveryEmailEnabled" type="checkbox" class="h-4 w-4">
                                 <span>管理员每条回复都发送邮件提醒</span>
                             </label>
                             <div class="flex items-center gap-2">
                                 <input id="togglePreExpiryRenewalEmailEnabled" type="checkbox" class="h-4 w-4">
                                 <span>启用到期前邮件续费提醒</span>
                                 <input id="inputPreExpiryRenewalDays" type="number" min="0" value="3" class="ml-2 w-24 px-2 py-1 border rounded-md" placeholder="提前天数">
                                 <span class="text-sm text-gray-600">天</span>
                             </div>
                             <div class="flex items-center gap-2">
                                 <input id="togglePreExpiryRenewalRepeatEnabled" type="checkbox" class="h-4 w-4">
                                 <span>每次自动检查都发送提醒邮件</span>
                                 <span class="text-xs text-gray-500">(关闭后仅在群聊第一次到期时发送)</span>
                             </div>
                            <p class="text-xs text-gray-500">关闭后，相关前后端接口将不可用或隐藏。</p>
                            
                            <!-- 邮件模板设置 -->
                            <div class="mt-6 pt-4 border-t border-gray-200">
                                <h4 class="font-medium text-gray-700 mb-3">邮件模板</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center gap-2">
                                        <label class="block text-sm w-40">到期提醒模板</label>
                                        <select id="settingEmailTemplateEnd" class="mt-1 flex-1 px-3 py-2 border rounded-md"></select>
                                        <button id="refreshEmailTemplates" class="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md">刷新</button>
                                    </div>
                                    <p class="text-xs text-gray-500 ml-40">目录：public/email_end</p>
                                    <div class="flex items-center gap-2">
                                        <label class="block text-sm w-40">验证码模板</label>
                                        <select id="settingEmailTemplateCaptcha" class="mt-1 flex-1 px-3 py-2 border rounded-md"></select>
                                    </div>
                                    <p class="text-xs text-gray-500 ml-40">目录：public/email_captcha</p>
                                    <div class="flex items-center gap-2">
                                        <label class="block text-sm w-40">工单回复模板</label>
                                        <select id="settingEmailTemplateWorkOrder" class="mt-1 flex-1 px-3 py-2 border rounded-md"></select>
                                    </div>
                                    <p class="text-xs text-gray-500 ml-40">目录：public/email_work_order</p>
                                    <div class="flex items-center gap-2">
                                        <label class="block text-sm w-40">普通消息模板</label>
                                        <select id="settingEmailTemplateMessages" class="mt-1 flex-1 px-3 py-2 border rounded-md"></select>
                                    </div>
                                    <p class="text-xs text-gray-500 ml-40">目录：public/email_messages</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 按次付费Tab内容 -->
            <div id="payPerUseContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
                <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
                    <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">按次付费管理</h2>
                </div>

                <!-- 按次付费子标签 -->
                <div class="border-b border-gray-200 mb-4 flex-shrink-0">
                    <nav class="-mb-px flex space-x-4 overflow-x-auto" aria-label="PayPerUse Tabs">
                        <button id="payPerUseFeaturesTab" class="pay-per-use-subtab active">
                            功能管理
                        </button>
                        <button id="payPerUseCodesTab" class="pay-per-use-subtab">
                            兑换码管理
                        </button>
                        <button id="payPerUseBillingTab" class="pay-per-use-subtab">
                            计费账户
                        </button>
                        <button id="payPerUseLogsTab" class="pay-per-use-subtab">
                            使用日志
                        </button>
                    </nav>
                </div>

                <!-- 功能管理子内容 -->
                <div id="payPerUseFeaturesContent" class="pay-per-use-subcontent flex-1 flex flex-col min-h-0">
                    <div class="flex justify-between items-center mb-4 flex-shrink-0">
                        <h3 class="text-lg font-medium text-slate-700">功能列表</h3>
                        <button id="createPayPerUseFeatureButton" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            新增功能
                        </button>
                    </div>
                    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
                        <table id="payPerUseFeaturesTable" class="admin-table-container responsive-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>功能代码</th>
                                    <th>显示名称</th>
                                    <th>描述</th>
                                    <th>API端点</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="payPerUseFeaturesTableBody">
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>



                <!-- 兑换码管理子内容 -->
                <div id="payPerUseCodesContent" class="pay-per-use-subcontent hidden flex-1 flex flex-col min-h-0">
                    <div class="flex justify-between items-center mb-4 flex-shrink-0">
                        <h3 class="text-lg font-medium text-slate-700">兑换码管理</h3>
                        <button id="generatePayPerUseCodesButton" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            生成兑换码
                        </button>
                    </div>
                    <div class="mb-4 flex gap-2 flex-shrink-0">
                        <input type="text" id="payPerUseCodesSearchInput" placeholder="搜索兑换码"
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button id="searchPayPerUseCodesButton" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            搜索
                        </button>
                    </div>
                    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
                        <table id="payPerUseCodesTable" class="admin-table-container responsive-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>兑换码</th>
                                    <th>功能名称</th>
                                    <th>备注</th>
                                    <th>批次ID</th>
                                    <th>状态</th>
                                    <th>过期时间</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="payPerUseCodesTableBody">
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 flex justify-between items-center flex-shrink-0">
                        <div id="payPerUseCodesPaginationInfo" class="text-sm text-gray-600"></div>
                        <div class="flex gap-2">
                            <button id="payPerUseCodesPrevPage" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded">上一页</button>
                            <button id="payPerUseCodesNextPage" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded">下一页</button>
                        </div>
                    </div>
                </div>

                <!-- 计费账户管理子内容 -->
                <div id="payPerUseBillingContent" class="pay-per-use-subcontent hidden flex-1 flex flex-col min-h-0">
                    <div class="flex justify-between items-center mb-4 flex-shrink-0">
                        <h3 class="text-lg font-medium text-slate-700">计费账户管理</h3>
                    </div>
                    <div class="mb-4 flex gap-2 flex-shrink-0">
                        <input type="text" id="payPerUseBillingSearchInput" placeholder="搜索群号"
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button id="searchPayPerUseBillingButton" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            搜索
                        </button>
                    </div>
                    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
                        <table id="payPerUseBillingTable" class="admin-table-container responsive-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>群号</th>
                                    <th>功能名称</th>
                                    <th>剩余次数</th>
                                    <th>累计购买</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="payPerUseBillingTableBody">
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 flex justify-between items-center flex-shrink-0">
                        <div id="payPerUseBillingPaginationInfo" class="text-sm text-gray-600"></div>
                        <div class="flex gap-2">
                            <button id="payPerUseBillingPrevPage" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded">上一页</button>
                            <button id="payPerUseBillingNextPage" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded">下一页</button>
                        </div>
                    </div>
                </div>

                <!-- 使用日志子内容 -->
                <div id="payPerUseLogsContent" class="pay-per-use-subcontent hidden flex-1 flex flex-col min-h-0">
                    <div class="flex justify-between items-center mb-4 flex-shrink-0">
                        <h3 class="text-lg font-medium text-slate-700">使用日志</h3>
                    </div>
                    <div class="mb-4 flex gap-2 flex-shrink-0">
                        <input type="text" id="payPerUseLogsGroupSearchInput" placeholder="搜索群号"
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="date" id="payPerUseLogsDateFromInput" class="px-3 py-2 border border-gray-300 rounded-md">
                        <input type="date" id="payPerUseLogsDateToInput" class="px-3 py-2 border border-gray-300 rounded-md">
                        <button id="searchPayPerUseLogsButton" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            搜索
                        </button>
                    </div>
                    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
                        <table id="payPerUseLogsTable" class="admin-table-container responsive-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>群号</th>
                                    <th>功能名称</th>
                                    <th>使用次数</th>
                                    <th>请求备注</th>
                                    <th>使用时间</th>
                                </tr>
                            </thead>
                            <tbody id="payPerUseLogsTableBody">
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 flex justify-between items-center flex-shrink-0">
                        <div id="payPerUseLogsPaginationInfo" class="text-sm text-gray-600"></div>
                        <div class="flex gap-2">
                            <button id="payPerUseLogsPrevPage" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded">上一页</button>
                            <button id="payPerUseLogsNextPage" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded">下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 按次付费功能管理弹窗 -->
            <div id="payPerUseFeatureModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4" id="payPerUseFeatureModalTitle">新增功能</h3>
                            <form id="payPerUseFeatureForm" class="space-y-4">
                                <input type="hidden" id="payPerUseFeatureId" value="">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">功能代码</label>
                                    <input type="text" id="payPerUseFeatureCode" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">显示名称</label>
                                    <input type="text" id="payPerUseFeatureName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                                    <textarea id="payPerUseFeatureDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">API端点</label>
                                    <input type="text" id="payPerUseFeatureEndpoint" class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600" placeholder="自动生成" readonly>
                                    <p class="text-xs text-gray-500 mt-1">API端点将根据功能代码自动生成</p>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="payPerUseFeatureActive" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label class="ml-2 block text-sm text-gray-700">启用</label>
                                </div>
                                <div class="flex justify-end space-x-3 pt-4">
                                    <button type="button" id="cancelPayPerUseFeature" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">取消</button>
                                    <button type="submit" id="savePayPerUseFeature" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">保存</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>



            <!-- 生成兑换码弹窗 -->
            <div id="payPerUseCodeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">生成兑换码</h3>
                            <form id="payPerUseCodeForm" class="space-y-4">
                                <div id="payPerUseCodeFeaturesContainer">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">功能配置</label>
                                    <p class="text-xs text-gray-500 mb-2">为每个功能设置不同的使用次数</p>
                                    <!-- 动态生成的功能选择区域 -->
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">生成数量</label>
                                    <input type="number" id="payPerUseCodeQuantity" min="1" max="1000" value="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">批次ID（可选）</label>
                                    <input type="text" id="payPerUseCodeBatchId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="用于标识这批兑换码">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">备注（可选）</label>
                                    <textarea id="payPerUseCodeNote" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">过期时间（可选）</label>
                                    <input type="datetime-local" id="payPerUseCodeExpiry" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div class="flex justify-end space-x-3 pt-4">
                                    <button type="button" id="cancelPayPerUseCode" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">取消</button>
                                    <button type="submit" id="generatePayPerUseCode" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md">生成兑换码</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 生成结果显示弹窗 -->
            <div id="payPerUseCodeResultModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">生成的兑换码</h3>
                            <div id="payPerUseCodeResultList" class="max-h-96 overflow-y-auto mb-4 p-3 bg-gray-50 rounded border">
                                <!-- 生成的兑换码列表 -->
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button type="button" id="copyPayPerUseCodes" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">复制所有兑换码</button>
                                <button type="button" id="closePayPerUseCodeResult" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

<!-- 用户管理Tab内容 -->
<div id="usersContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
        <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">用户管理</h2>
        <div class="flex gap-2">
            <button id="createUserButton"
                class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                创建用户
            </button>
            <input type="text" id="userSearchInput" placeholder="搜索用户名/邮箱/QQ" 
                class="px-3 py-2 border border-gray-300 rounded-md w-64">
            <button id="refreshUsersButton"
                class="flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                刷新
            </button>
                     <button id="openSendEmailModalButton"
                         class="flex items-center justify-center bg-gray-700 hover:bg-gray-800 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                         发送邮件
                     </button>
        </div>
    </div>

    <div id="usersLoadingMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
        <div class="admin-loader inline-block mr-2"></div> 加载中，请稍候...
    </div>

    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
        <table id="usersTable" class="admin-table-container responsive-table">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        ID
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        用户名
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        邮箱
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        QQ
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        注册时间
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        绑定数
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        状态
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        操作
                    </th>
                </tr>
            </thead>
            <tbody id="users-table-body" class="bg-white divide-y divide-gray-200">
            </tbody>
        </table>
    </div>

</div>

<!-- 功能黑名单管理Tab内容 -->
<div id="featureBlacklistContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
        <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">功能黑名单定义</h2>
        <button id="addFeatureButton"
            class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            添加功能定义
        </button>
    </div>
    
    <div id="featureBlacklistLoadingMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
        <div class="admin-loader inline-block mr-2"></div> 加载中，请稍候...
    </div>
    
    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
        <table id="featureBlacklistTable" class="admin-table-container responsive-table hidden">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        显示名称
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        实际功能
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        请求标识符
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        可用套餐
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        操作
                    </th>
                </tr>
            </thead>
            <tbody id="feature-blacklist-table-body" class="bg-white divide-y divide-gray-200">
            </tbody>
        </table>
    </div>
</div>

<!-- 功能白名单管理Tab内容 -->
<div id="featureWhitelistContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
        <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">功能白名单定义</h2>
        <button id="addWhitelistFeatureButton"
            class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white font-semibold py-2.5 px-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            添加功能定义
        </button>
    </div>
    
    <div id="featureWhitelistLoadingMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
        <div class="admin-loader inline-block mr-2"></div> 加载中，请稍候...
    </div>
    
    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
        <table id="featureWhitelistTable" class="admin-table-container responsive-table hidden">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        显示名称
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        实际功能
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        请求标识符
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        可用套餐
                    </th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                        操作
                    </th>
                </tr>
            </thead>
            <tbody id="feature-whitelist-table-body" class="bg-white divide-y divide-gray-200">
            </tbody>
        </table>
    </div>
</div>

<!-- 工单管理Tab内容 -->
<div id="ticketsContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
        <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">工单管理</h2>
        <div class="flex flex-wrap gap-2">
            <button id="refreshTicketsButton" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                刷新
            </button>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="bg-gray-50 p-4 rounded-lg mb-4 flex-shrink-0">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">状态筛选</label>
                <select id="ticketStatusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">全部状态</option>
                    <option value="open">待处理</option>
                    <option value="in_progress">处理中</option>
                    <option value="waiting">等待回复</option>
                    <option value="closed">已关闭</option>
                    <option value="reopened">重新打开</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">类型筛选</label>
                <select id="ticketTypeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">全部类型</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">优先级筛选</label>
                <select id="ticketPriorityFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">全部优先级</option>
                    <option value="low">低</option>
                    <option value="normal">普通</option>
                    <option value="high">高</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                <input type="text" id="ticketSearchInput" placeholder="搜索标题、内容、用户名" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
        </div>
    </div>

    <!-- 工单列表 -->
    <div class="overflow-x-auto overflow-y-auto shadow-md rounded-lg flex-1 min-h-0">
        <table id="ticketsTable" class="admin-table-container responsive-table hidden">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">工单号</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">标题</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">类型</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">优先级</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">用户</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody id="ticketsTableBody" class="bg-white divide-y divide-gray-200">
                <!-- 工单数据将在这里动态加载 -->
            </tbody>
        </table>
    </div>

    <!-- 分页 -->
    <div id="ticketsPagination" class="mt-4 flex items-center justify-between flex-shrink-0">
        <!-- 分页控件将在这里动态加载 -->
    </div>
</div>

<!-- 工单类型管理Tab内容 -->
<div id="ticketTypesContent" class="tab-content hidden flex-1 flex flex-col min-h-0">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4 flex-shrink-0">
        <h2 class="text-2xl sm:text-3xl font-semibold text-slate-700">工单类型管理</h2>
        <button id="addTicketTypeButton" onclick="TicketTypesModule.addTicketType()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            添加类型
        </button>
    </div>

    <!-- 加载状态指示器 -->
    <div id="ticketTypesLoadingMessage" class="text-center py-10 text-gray-500 hidden flex-shrink-0">
        <div class="admin-loader inline-block mr-2"></div> 加载中，请稍候...
    </div>

    <!-- 工单类型列表 -->
    <div class="overflow-x-auto shadow-md rounded-lg flex-1 min-h-0">
        <table id="ticketTypesTable" class="admin-table-container responsive-table">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">名称</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">描述</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">排序</th>
                    <th scope="col" class="px-4 py-3.5 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody id="ticketTypesTableBody" class="bg-white divide-y divide-gray-200">
                <!-- 工单类型数据将在这里动态加载 -->
            </tbody>
        </table>
    </div>
</div>

<!-- 工单详情模态框 -->
<div id="ticketDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">工单详情</h3>
            <button id="closeTicketDetailModal" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div id="ticketDetailContent" class="space-y-4">
            <!-- 工单详情内容将在这里动态加载 -->
        </div>
        
        <!-- 工单回复区域 -->
        <div class="mt-6 border-t pt-4">
            <h4 class="text-md font-medium text-gray-900 mb-3">工单回复</h4>
            <div id="ticketRepliesList" class="space-y-3 mb-4">
                <!-- 回复列表将在这里动态加载 -->
            </div>
            
            <form id="adminReplyForm" class="space-y-3">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">回复内容</label>
                    <textarea id="adminReplyContent" rows="3" placeholder="请输入回复内容" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md resize-none"></textarea>
                </div>
                <div class="flex justify-between">
                    <div class="flex space-x-2">
                        <button type="submit" id="submitAdminReplyButton" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                            提交回复
                        </button>
                        <button type="button" id="closeTicketButton" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                            关闭工单
                        </button>
                    </div>
                    <div class="flex space-x-2">
                        <select id="ticketStatusSelect" class="px-3 py-2 border border-gray-300 rounded-md">
                            <option value="open">待处理</option>
                            <option value="in_progress">处理中</option>
                            <option value="waiting">等待回复</option>
                            <option value="closed">已关闭</option>
                            <option value="reopened">重新打开</option>
                        </select>
                        <button type="button" id="updateTicketStatusButton" 
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md transition-colors">
                            更新状态
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 工单类型编辑模态框 -->
<div id="ticketTypeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 id="ticketTypeModalTitle" class="text-lg font-medium text-gray-900">添加工单类型</h3>
            <button id="closeTicketTypeModal" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="ticketTypeForm" class="space-y-4">
            <input type="hidden" id="ticketTypeId" value="">
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">类型名称 *</label>
                <input type="text" id="ticketTypeName" required maxlength="50"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                <textarea id="ticketTypeDescription" rows="3" maxlength="200"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md resize-none"></textarea>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">排序</label>
                <input type="number" id="ticketTypeSortOrder" min="0" value="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="ticketTypeActive" checked
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label class="ml-2 block text-sm text-gray-900">启用</label>
            </div>
            
            <div class="flex justify-end space-x-2 pt-2">
                <button type="button" id="cancelTicketTypeButton" 
                        class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                    取消
                </button>
                <button type="submit" id="saveTicketTypeButton" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    保存
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 用户编辑模态框 -->




<!-- 功能黑名单编辑模态框 -->
<div id="featureModal"
    class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
    aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
        <div class="flex justify-between items-center mb-6">
            <h3 id="featureModalTitle" class="text-2xl font-semibold text-slate-800">添加功能定义</h3>
            <button type="button" id="closeFeatureModal"
                class="text-gray-400 hover:text-gray-600 transition-colors">
                <span class="sr-only">关闭</span>
                <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <form id="featureForm" class="space-y-5">
            <input type="hidden" id="featureId">
            
            <div>
                <label for="featureDisplayName" class="block text-sm font-medium text-gray-700 mb-1">显示名称</label>
                <input type="text" id="featureDisplayName" required placeholder="例如：文本生成功能"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
            </div>
            
            <div>
                <label for="featureActualFeatures" class="block text-sm font-medium text-gray-700 mb-1">实际功能</label>
                <input type="text" id="featureActualFeatures" required placeholder="用逗号分隔，例如：ddd,123,456"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                <p class="mt-1 text-xs text-gray-500">多个功能用英文逗号分隔</p>
            </div>
            
            <div>
                <label for="featureRequestIdentifiers" class="block text-sm font-medium text-gray-700 mb-1">请求标识符</label>
                <input type="text" id="featureRequestIdentifiers" required placeholder="用逗号分隔，例如：gpt,claude,gemini"
                    class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                <p class="mt-1 text-xs text-gray-500">机器人框架用于识别功能的标识符，多个用英文逗号分隔</p>
            </div>
            
            <div>
                <label for="featureAllowedTiers" class="block text-sm font-medium text-gray-700 mb-1">可用套餐</label>
                <div id="featureTiersCheckboxes" class="space-y-2 mt-1">
                    <!-- 套餐复选框将在这里动态生成 -->
                </div>
                <p class="mt-1 text-xs text-gray-500">选择可以使用此功能黑名单的套餐</p>
            </div>

            <div id="featureModalError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

            <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                <button type="submit" id="saveFeatureButton"
                    class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform transform hover:scale-105">
                    <span class="button-text">保存</span>
                    <span class="admin-loader hidden ml-2"></span>
                </button>
                <button type="button" id="cancelFeatureButton"
                    class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                    取消
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 广播模态框 -->
<div id="broadcastModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            群聊广播
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                <span id="broadcastInfo">已选择 0 个群聊</span>
                            </p>
                        </div>
                        <div class="mt-4">
                            <label for="broadcastMessage" class="block text-sm font-medium text-gray-700">消息内容</label>
                            <textarea id="broadcastMessage" rows="4" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                placeholder="请输入要广播的消息内容..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmBroadcast"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm">
                    发送
                </button>
                <button type="button" id="cancelBroadcast"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    取消
                </button>
            </div>
        </div>
    </div>
</div>
<!-- 通用模态框 -->
<div id="genericModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-2xl w-full p-6 sm:p-8 transform transition-all">
        <div class="flex justify-between items-center mb-6">
            <h3 id="genericModalTitle" class="text-2xl font-semibold text-slate-800">模态框标题</h3>
            <button type="button" id="closeGenericModal" class="text-gray-400 hover:text-gray-600 transition-colors">
                <span class="sr-only">关闭</span>
                <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div id="genericModalContent" class="text-gray-700">
            <!-- 动态内容将在这里插入 -->
        </div>
    </div>
</div>

<!-- 退群确认模态框 -->
<div id="leaveGroupModal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            确认退群
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500" id="leaveGroupInfo">
                                确定要让机器人退出该群吗？
                            </p>
                        </div>
                        <div class="mt-4">
                            <label for="leaveGroupRobot" class="block text-sm font-medium text-gray-700">选择要退群的机器人</label>
                            <select id="leaveGroupRobot" 
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmLeaveGroup"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    确认退群
                </button>
                <button type="button" id="cancelLeaveGroup"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    取消
                </button>
            </div>
        </div>
    </div>
</div>

    <!-- 绑定模态框 -->
    <div id="bindingModal"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div
            class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
            <div class="flex justify-between items-center mb-6">
                <h3 id="bindingModalTitle" class="text-2xl font-semibold text-slate-800">模态框标题</h3>
                <button type="button" id="closeBindingModal"
                    class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="bindingForm" class="space-y-5">
                <input type="hidden" id="originalOrderNumber">
                <input type="hidden" id="originalSkuId">

                <div>
                    <label for="modalOrderNumber" class="block text-sm font-medium text-gray-700 mb-1">激活码</label>
                    <input type="text" id="modalOrderNumber" required placeholder="输入激活码或留空自动生成"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>
                <div>
                    <label for="modalGroupNumber" class="block text-sm font-medium text-gray-700 mb-1">绑定群号</label>
                    <input type="text" id="modalGroupNumber" required
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>
                <div>
                    <label for="modalSkuId" class="block text-sm font-medium text-gray-700 mb-1">选择档位</label>
                    <select id="modalSkuId" required
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                    </select>
                </div>

                <div>
                    <div class="flex items-center justify-between mb-1">
                        <label for="modalExpirationTimestamp"
                            class="block text-sm font-medium text-gray-700">到期时间</label>
                        <div class="flex items-center">
                            <input type="checkbox" id="modalPermanentExpiration"
                                class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                            <label for="modalPermanentExpiration" class="ml-2 block text-sm text-gray-900">永久有效</label>
                        </div>
                    </div>
                    <input type="datetime-local" id="modalExpirationTimestamp"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                    <p class="mt-1 text-xs text-gray-500">选择具体的到期日期和时间。如果勾选 "永久有效"，此项将被忽略。</p>
                </div>

                <div id="bindingModalError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

                <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                    <button type="submit" id="saveBindingButton"
                        class="w-full sm:w-auto flex justify-center items-center py-
                        class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform transform hover:scale-105">
                        <span class="button-text">保存</span>
                        <span class="admin-loader hidden ml-2"></span>
                    </button>
                    <button type="button" id="cancelBindingButton"
                        class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 创建兑换码模态框 -->
    <div id="createCodesModal"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div
            class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-semibold text-slate-800">批量创建兑换码</h3>
                <button type="button" id="closeCreateCodesModal"
                    class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="createCodesForm" class="space-y-5">
                <div>
                    <label for="codesSkuId" class="block text-sm font-medium text-gray-700 mb-1">选择档位</label>
                    <select id="codesSkuId" required
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                    </select>
                </div>
                <div>
                    <label for="codesDuration" class="block text-sm font-medium text-gray-700 mb-1">有效时长（月）</label>
                    <input type="number" id="codesDuration" required min="1" max="120" value="1"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>
                <div>
                    <label for="codesQuantity" class="block text-sm font-medium text-gray-700 mb-1">创建数量</label>
                    <input type="number" id="codesQuantity" required min="1" max="1000" value="10"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>
                <div>
                    <label for="codesNote" class="block text-sm font-medium text-gray-700 mb-1">备注（可选）</label>
                    <input type="text" id="codesNote" placeholder="批次备注信息"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>

                <div id="createCodesError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

                <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                    <button type="submit" id="confirmCreateCodesButton"
                        class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-transform transform hover:scale-105">
                        <span class="button-text">创建</span>
                        <span class="admin-loader hidden ml-2"></span>
                    </button>
                    <button type="button" id="cancelCreateCodesButton"
                        class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 创建结果模态框 -->
    <div id="codesResultModal"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div
            class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-2xl w-full p-6 sm:p-8 transform transition-all">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-semibold text-slate-800">创建成功</h3>
                <button type="button" id="closeCodesResultModal"
                    class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <div id="codesResultContent" class="space-y-4">
                <!-- 创建结果将在这里显示 -->
            </div>

            <div class="mt-6 flex justify-end gap-3">
                <button id="copyCodesButton"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
                    复制所有兑换码
                </button>
                <button id="downloadCodesButton"
                    class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md">
                    下载为文本文件
                </button>
            </div>
        </div>
    </div>

    <!-- 机器人编辑模态框 -->
    <div id="robotModal"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div
            class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
            <div class="flex justify-between items-center mb-6">
                <h3 id="robotModalTitle" class="text-2xl font-semibold text-slate-800">添加机器人</h3>
                <button type="button" id="closeRobotModal"
                    class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="robotForm" class="space-y-5">
                <input type="hidden" id="robotId">
                
                <div>
                    <label for="robotAccount" class="block text-sm font-medium text-gray-700 mb-1">机器人账号</label>
                    <input type="text" id="robotAccount" required placeholder="输入机器人QQ号"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                    <p class="mt-1 text-xs text-gray-500">机器人账号创建后不可修改</p>
                </div>
                
                <div>
                    <label for="robotName" class="block text-sm font-medium text-gray-700 mb-1">机器人名称</label>
                    <input type="text" id="robotName" required placeholder="给机器人起个名字"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>
                
                <div>
                    <label for="robotApiUrl" class="block text-sm font-medium text-gray-700 mb-1">API地址</label>
                    <input type="url" id="robotApiUrl" required placeholder="http://192.168.1.100:6102/"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                    <p class="mt-1 text-xs text-gray-500">请输入完整的API地址，包含协议和端口</p>
                </div>
                
                <div>
                    <label for="robotApiToken" class="block text-sm font-medium text-gray-700 mb-1">API Token（可选）</label>
                    <input type="text" id="robotApiToken" placeholder="如果API需要认证，请输入Token"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>

                <div id="robotModalError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

                <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                    <button type="submit" id="saveRobotButton"
                        class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform transform hover:scale-105">
                        <span class="button-text">保存</span>
                        <span class="admin-loader hidden ml-2"></span>
                    </button>
                    <button type="button" id="cancelRobotButton"
                        class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Toast通知 -->
    <div id="toast-notification"
        class="fixed bottom-5 right-5 bg-gray-800 text-white py-3 px-5 rounded-lg shadow-lg hidden transition-opacity duration-300 ease-in-out z-[100]">
        <p id="toast-message"></p>
    </div>

    <!-- 图片预览模态框 -->
    <div id="imagePreviewModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[200] hidden">
        <div class="relative max-w-5xl max-h-[90vh] w-[90vw]">
            <button id="closeImagePreview" class="absolute -top-10 right-0 text-white bg-black/50 hover:bg-black/70 px-3 py-1 rounded">关闭</button>
            <img id="imagePreviewImg" src="" alt="预览" class="w-full h-auto max-h-[90vh] object-contain rounded shadow-lg" />
        </div>
    </div>

    

    



    <script src="js/bindings.js"></script>
    <script src="js/codes.js"></script>
    <script src="js/robots.js"></script>
    <script src="js/groups.js"></script>
    <script src="js/feature-blacklist.js"></script>
    <script src="js/feature-whitelist.js"></script>

    <!-- 用户管理模态框 -->
    <!-- 创建用户模态框 -->
    <div id="createUserModal"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div
            class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
            <div class="flex justify-between items-center mb-6">
                <h3 id="createUserModalTitle" class="text-2xl font-semibold text-slate-800">创建新用户</h3>
                <button type="button" id="closeCreateUserModal"
                    class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="createUserForm" class="space-y-5">
                <div>
                    <label for="createUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名 *</label>
                    <input type="text" id="createUsername" required placeholder="请输入用户名"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>

                <div>
                    <label for="createEmail" class="block text-sm font-medium text-gray-700 mb-1">邮箱 *</label>
                    <input type="email" id="createEmail" required placeholder="请输入邮箱地址"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>

                <div>
                    <label for="createPassword" class="block text-sm font-medium text-gray-700 mb-1">密码 *</label>
                    <input type="password" id="createPassword" required placeholder="请输入密码（至少6位）"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>

                <div>
                    <label for="createQQ" class="block text-sm font-medium text-gray-700 mb-1">QQ号 *</label>
                    <input type="text" id="createQQ" required placeholder="请输入QQ号"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>

                <div id="createUserModalError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

                <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                    <button type="submit" id="saveCreateUserButton"
                        class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-transform transform hover:scale-105">
                        <span class="button-text">创建用户</span>
                        <span class="admin-loader hidden ml-2"></span>
                    </button>
                    <button type="button" id="cancelCreateUserButton"
                        class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div id="userEditModal"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
            <div class="flex justify-between items-center mb-6">
                <h3 id="userEditModalTitle" class="text-2xl font-semibold text-slate-800">编辑用户</h3>
                <button type="button" id="closeUserEditModal"
                    class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="userEditForm" class="space-y-5">
                <input type="hidden" id="editUserId">

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="editUsername" disabled
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm bg-gray-100 sm:text-sm">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                    <input type="email" id="editEmail" disabled
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm bg-gray-100 sm:text-sm">
                </div>

                <div>
                    <label for="editQQ" class="block text-sm font-medium text-gray-700 mb-1">QQ号</label>
                    <input type="text" id="editQQ" placeholder="输入新的QQ号"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>

                <div>
                    <label for="editPassword" class="block text-sm font-medium text-gray-700 mb-1">新密码（留空不修改）</label>
                    <input type="password" id="editPassword" placeholder="输入新密码"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>

                <div id="userEditError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

                <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                    <button type="submit" id="saveUserButton"
                        class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform transform hover:scale-105">
                        <span class="button-text">保存</span>
                        <span class="admin-loader hidden ml-2"></span>
                    </button>
                    <button type="button" id="cancelUserEditButton"
                        class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 封禁用户模态框 -->
    <div id="banUserModal"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
            <div class="flex justify-between items-center mb-6">
                <h3 id="banUserModalTitle" class="text-2xl font-semibold text-slate-800">封禁用户</h3>
                <button type="button" id="closeBanUserModal"
                    class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="banUserForm" class="space-y-5">
                <input type="hidden" id="banUserId">

                <div>
                    <p class="text-sm text-gray-600 mb-4">确定要封禁用户 <span id="banUsername" class="font-semibold"></span> 吗？</p>
                    <label for="banReason" class="block text-sm font-medium text-gray-700 mb-1">封禁原因</label>
                    <textarea id="banReason" rows="3" required
                        placeholder="请输入封禁原因"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors"></textarea>
                </div>

                <div id="banUserError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

                <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                    <button type="submit" id="confirmBanButton"
                        class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-transform transform hover:scale-105">
                        <span class="button-text">确认封禁</span>
                        <span class="admin-loader hidden ml-2"></span>
                    </button>
                    <button type="button" id="cancelBanButton"
                        class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 发送邮件模态框 -->
    <div id="sendEmailModal" class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden" role="dialog" aria-modal="true">
        <div class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-semibold text-slate-800">发送邮件</h3>
                <button type="button" id="closeSendEmailModal" class="text-gray-400 hover:text-gray-600 transition-colors" aria-label="关闭">
                    <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
            </div>
            <form id="sendEmailForm" class="space-y-4">
                <input type="hidden" id="sendEmailUserId">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">收件人</label>
                    <input type="text" id="sendEmailTo" disabled class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">主题</label>
                    <input type="text" id="sendEmailSubject" placeholder="请输入主题" class="w-full px-3 py-2 border border-gray-300 rounded-md" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">内容</label>
                    <textarea id="sendEmailContent" rows="6" placeholder="请输入正文内容（支持换行）" class="w-full px-3 py-2 border border-gray-300 rounded-md resize-none"></textarea>
                </div>
                <div id="sendEmailError" class="text-sm text-red-600 min-h-[1.25rem]"></div>
                <div class="flex justify-end gap-3 pt-2">
                    <button type="button" id="cancelSendEmailButton" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">取消</button>
                    <button type="submit" id="confirmSendEmailButton" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">发送</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 编辑邮箱模态框 -->
    <div id="editEmailModal"
        class="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
        aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div
            class="modal-content-admin bg-white rounded-xl shadow-2xl overflow-hidden max-w-lg w-full p-6 sm:p-8 transform transition-all">
            <div class="flex justify-between items-center mb-6">
                <h3 id="editEmailModalTitle" class="text-2xl font-semibold text-slate-800">编辑用户邮箱</h3>
                <button type="button" id="closeEditEmailModal"
                    class="text-gray-400 hover:text-gray-600 transition-colors">
                    <span class="sr-only">关闭</span>
                    <svg class="h-7 w-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="editEmailForm" class="space-y-5">
                <input type="hidden" id="editEmailUserId">
                <input type="hidden" id="editEmailOldEmail">

                <div>
                    <label for="editEmailCurrent" class="block text-sm font-medium text-gray-700 mb-1">当前邮箱</label>
                    <input type="email" id="editEmailCurrent" disabled class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md bg-gray-100">
                </div>

                <div>
                    <label for="editEmailNew" class="block text-sm font-medium text-gray-700 mb-1">新邮箱 *</label>
                    <input type="email" id="editEmailNew" required placeholder="请输入新邮箱地址"
                        class="mt-1 block w-full px-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors">
                </div>

                <div id="editEmailModalError" class="text-sm text-red-600 min-h-[1.25rem]" aria-live="polite"></div>

                <div class="flex flex-col sm:flex-row-reverse sm:space-x-reverse sm:space-x-3 pt-3 gap-3 sm:gap-0">
                    <button type="submit" id="saveEditEmailButton"
                        class="w-full sm:w-auto flex justify-center items-center py-2.5 px-6 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-transform transform hover:scale-105">
                        <span class="button-text">保存</span>
                        <span class="admin-loader hidden ml-2"></span>
                    </button>
                    <button type="button" id="cancelEditEmailButton"
                        class="w-full sm:w-auto justify-center py-2.5 px-6 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/users.js"></script>
    <script src="js/ticket-management.js"></script>
    <script src="js/ticket-types.js"></script>
    <script src="js/tiers.js"></script>
    <script src="js/system-logs.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/pay-per-use.js"></script>
    <script src="js/admin.js"></script>
</body>

</html>
