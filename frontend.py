from flask import request, jsonify, send_file
from datetime import datetime, timedelta
import bcrypt
import jwt
from config import JWT_SECRET, logger, get_sku_type, config, append_system_log
import os

# 用户附件上传配置（与后台保持一致）
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'zip', 'rar'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def _allowed_file(filename: str) -> bool:
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def register_frontend_routes(app, db):
    """注册前端相关路由"""
    
    def _bool_from_setting(val: str, default_true: bool = True) -> bool:
        if val is None:
            return default_true
        return str(val).strip() in ('1', 'true', 'True', 'yes', 'on')

    def _ensure_feature_enabled_or_403(setting_key: str, feature_name: str):
        enabled = _bool_from_setting(db.get_setting(setting_key, '1'))
        if not enabled:
            return jsonify({'success': False, 'message': f'{feature_name} 功能已关闭'}), 403
        return None
    
    @app.route('/register', methods=['POST'])
    def register():
        data = request.json
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        qq = data.get('qq', '').strip()
        captcha = (data.get('captcha') or '').strip()
        
        if not all([username, email, password, qq]):
            return jsonify({'success': False, 'message': '所有字段都不能为空'}), 400
        
        if len(username) < 3 or len(username) > 20:
            return jsonify({'success': False, 'message': '用户名长度必须在3-20个字符之间'}), 400
        
        if len(password) < 6:
            return jsonify({'success': False, 'message': '密码长度必须至少6个字符'}), 400
        
        if not qq.isdigit() or len(qq) < 5 or len(qq) > 18:
            return jsonify({'success': False, 'message': 'QQ号格式不正确'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # Check if username exists
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '该用户名已被注册'}), 400
            
            # Check if email exists
            cursor.execute('SELECT id FROM users WHERE email = ?', (email,))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '该邮箱已被注册'}), 400
            
            # Check if QQ exists
            cursor.execute('SELECT id FROM users WHERE qq = ?', (qq,))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '该QQ号已被注册'}), 400
            
            # 校验邮箱后缀（若配置）
            try:
                import re as _re
                allowed_suffixes = (db.get_setting('allowed_email_suffixes', '') or '').strip()
                if allowed_suffixes:
                    suffixes = [s.strip().lower() for s in _re.split(r'[\s,;]+', allowed_suffixes) if s.strip()]
                    email_lc = email.lower()
                    if not any(email_lc.endswith(suf if suf.startswith('@') else ('@' + suf)) for suf in suffixes):
                        return jsonify({'success': False, 'message': '该邮箱后缀不在允许范围内'}), 400
            except Exception:
                pass

            # 若开启注册邮箱验证，则先校验验证码
            reg_email_verify_enabled = str(db.get_setting('register_email_verification_enabled', '0')) in ('1','true','True','yes','on')
            if reg_email_verify_enabled:
                if not captcha:
                    return jsonify({'success': False, 'message': '请输入邮箱验证码'}), 400
                cursor.execute('''
                    SELECT id, expires_at FROM email_captchas
                    WHERE email = ? AND purpose = 'register' AND code = ? AND used = 0
                    ORDER BY id DESC LIMIT 1
                ''', (email, captcha))
                row = cursor.fetchone()
                if not row:
                    return jsonify({'success': False, 'message': '验证码无效或已过期'}), 400
                try:
                    if datetime.fromisoformat(row['expires_at']) < datetime.utcnow():
                        return jsonify({'success': False, 'message': '验证码已过期'}), 400
                except Exception:
                    pass
                cursor.execute('UPDATE email_captchas SET used = 1 WHERE id = ?', (row['id'],))
            
            # Hash password
            hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
            
            # Insert new user
            cursor.execute(
                'INSERT INTO users (username, email, password, qq) VALUES (?, ?, ?, ?)',
                (username, email, hashed.decode('utf-8'), qq)
            )
            db.conn.commit()
            
            logger.info(f'新用户注册成功: {username} (QQ: {qq})')
            
            return jsonify({
                'success': True,
                'message': '注册成功',
                'data': {
                    'username': username,
                    'email': email,
                    'qq': qq
                }
            })
            
        except Exception as e:
            logger.error('用户注册过程中出错:', e)
            return jsonify({'success': False, 'message': f'注册失败: {str(e)}'}), 500

    @app.route('/register/email-captcha', methods=['POST'])
    def send_register_email_captcha():
        """发送注册邮箱验证码（受开关控制）。"""
        enabled = str(db.get_setting('register_email_verification_enabled', '0')) in ('1','true','True','yes','on')
        if not enabled:
            return jsonify({'success': False, 'message': '邮箱验证功能未开启'}), 403
        data = request.get_json() or {}
        email = (data.get('email') or '').strip()
        if not email:
            return jsonify({'success': False, 'message': '邮箱不能为空'}), 400
        # 已注册邮箱禁止发送验证码
        try:
            cursor = db.conn.cursor()
            cursor.execute('SELECT id FROM users WHERE email = ?', (email,))
            if cursor.fetchone():
                return jsonify({'success': False, 'message': '该邮箱已被注册，请直接登录或更换邮箱'}), 400
        except Exception:
            pass
        # 校验邮箱后缀（若配置）
        try:
            import re as _re
            allowed_suffixes = (db.get_setting('allowed_email_suffixes', '') or '').strip()
            if allowed_suffixes:
                suffixes = [s.strip().lower() for s in _re.split(r'[\s,;]+', allowed_suffixes) if s.strip()]
                email_lc = email.lower()
                if not any(email_lc.endswith(suf if suf.startswith('@') else ('@' + suf)) for suf in suffixes):
                    return jsonify({'success': False, 'message': '该邮箱后缀不在允许范围内'}), 400
        except Exception:
            pass
        try:
            cursor = db.conn.cursor()
            cursor.execute("SELECT requested_at FROM email_captchas WHERE email = ? AND purpose = 'register' ORDER BY id DESC LIMIT 1", (email,))
            last = cursor.fetchone()
            if last:
                try:
                    last_dt = datetime.fromisoformat(last['requested_at'])
                    if (datetime.utcnow() - last_dt).total_seconds() < 60:
                        return jsonify({'success': False, 'message': '请求过于频繁，请稍后再试'}), 429
                except Exception:
                    pass
            import secrets
            code = f"{secrets.randbelow(1000000):06d}"
            ttl_minutes = 10
            expires_at = (datetime.utcnow() + timedelta(minutes=ttl_minutes)).isoformat()
            request_ip = request.headers.get('X-Forwarded-For', request.remote_addr)
            cursor.execute('''
                INSERT INTO email_captchas (email, purpose, code, requested_ip, expires_at)
                VALUES (?, 'register', ?, ?, ?)
            ''', (email, code, request_ip, expires_at))
            db.conn.commit()
        except Exception as e:
            logger.error('生成验证码记录失败:', e)
            return jsonify({'success': False, 'message': '内部错误'}), 500

        # 发送邮件（异步队列，不阻塞响应）
        try:
            subject = '注册邮箱验证码'
            content = '您正在申请注册账号，请输入以下验证码完成验证。'
            template_filename = (db.get_setting('email_template_captcha', '') or '').strip()
            site_name = db.get_setting('site_title', '') or ''
            site_link = db.get_setting('site_link', '') or ''
            support_contact = db.get_setting('support_contact', '') or ''
            html_body = None
            if template_filename:
                path = os.path.join('public', 'email_captcha', template_filename)
                if os.path.isfile(path):
                    with open(path, 'r', encoding='utf-8') as f:
                        html_body = f.read()
                    html_body = html_body.replace('{{subject}}', subject)
                    html_body = html_body.replace('{{site_name}}', site_name)
                    html_body = html_body.replace('{{site_link}}', site_link)
                    html_body = html_body.replace('{{contact}}', support_contact)
                    html_body = html_body.replace('{{content}}', content)
                    html_body = html_body.replace('{{code}}', code)
                    html_body = html_body.replace('{{ttl_minutes}}', str(ttl_minutes))
                    html_body = html_body.replace('{{request_ip}}', request_ip or '-')
                    html_body = html_body.replace('{{request_time}}', datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S'))
            if not html_body:
                html_body = (
                    f"<!doctype html><html><head><meta charset='utf-8'><title>{subject}</title></head>"
                    f"<body><div style='max-width:600px;margin:20px auto;font-family:Arial,Helvetica,\"Microsoft YaHei\",sans-serif;'>"
                    f"<h2 style='margin:0 0 10px 0;'>{subject}</h2>"
                    f"<div style='margin-top:12px;line-height:1.6'>{content}</div>"
                    f"<div style='margin-top:16px;font-size:28px;font-weight:bold;letter-spacing:6px;'>{code}</div>"
                    f"<div style='margin-top:8px;color:#888;font-size:12px;'>有效期：{ttl_minutes}分钟</div>"
                    f"</div></body></html>"
                )

            try:
                app.enqueue_email(email, subject, html_body, {'actor': 'auto', 'action': '发送注册验证码'})
                try:
                    append_system_log('auto', '注册/发送邮箱验证码', f'邮箱{email} 已入队')
                except Exception:
                    pass
                return jsonify({'success': True, 'message': '验证码已入队发送'})
            except Exception:
                # 回退：快速返回
                return jsonify({'success': True, 'message': '验证码已提交发送'})
        except Exception as e:
            logger.error('发送注册验证码失败:', e)
            return jsonify({'success': False, 'message': '发送失败'}), 500
    
    @app.route('/login', methods=['POST'])
    def login():
        data = request.json
        username = data.get('username', '').strip()
        password = data.get('password', '')
        
        if not username or not password:
            return jsonify({'success': False, 'message': '用户名和密码均不能为空'}), 400
        
        try:
            cursor = db.conn.cursor()
            cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
            user = cursor.fetchone()
            
            if not user:
                return jsonify({'success': False, 'message': '用户名或密码错误'}), 401
                
            # 检查用户是否被封禁
            # 使用索引访问方式，因为SQLite3.Row不支持get方法
            try:
                is_banned = user['is_banned'] if user['is_banned'] is not None else 0
                if is_banned:
                    ban_reason = user['ban_reason'] if user['ban_reason'] else '违反使用条款'
                    return jsonify({
                        'success': False,
                        'message': f'您的账户已被封禁。原因：{ban_reason}'
                    }), 403
            except (KeyError, IndexError):
                # 如果字段不存在，说明是旧数据库，允许登录
                pass
                
            if not bcrypt.checkpw(password.encode('utf-8'), user['password'].encode('utf-8')):
                return jsonify({'success': False, 'message': '用户名或密码错误'}), 401
            
            # 生成JWT token
            token = jwt.encode(
                {
                    'username': user['username'],
                    'email': user['email'],
                    'exp': datetime.utcnow() + timedelta(days=7)
                },
                JWT_SECRET,
                algorithm='HS256'
            )
            
            logger.info(f'用户登录成功: {username}')
            
            return jsonify({
                'success': True,
                'message': '登录成功',
                'data': {
                    'username': user['username'],
                    'email': user['email'],
                    'qq': user['qq'],  # 添加QQ号到返回数据
                    'token': token,
                    'registerTime': user['register_time']
                }
            })
        except Exception as e:
            logger.error('用户登录过程中出错:', e)
            return jsonify({'success': False, 'message': f'登录失败: {str(e)}'}), 500
    @app.route('/user/profile', methods=['GET'])
    def get_user_profile():
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401

        try:
            cursor = db.conn.cursor()
            cursor.execute('SELECT username, email, qq, register_time, email_notify_enabled FROM users WHERE username = ?', (username,))
            row = cursor.fetchone()
            if not row:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            return jsonify({
                'success': True,
                'data': {
                    'username': row['username'],
                    'email': row['email'],
                    'qq': row['qq'],
                    'registerTime': row['register_time'],
                    'emailNotifyEnabled': bool(row['email_notify_enabled']) if row['email_notify_enabled'] is not None else True
                }
            })
        except Exception as e:
            logger.error('获取用户资料失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/user/email-notify', methods=['POST'])
    def update_email_notify():
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401

        try:
            data = request.get_json() or {}
            enabled = bool(data.get('enabled', True))
            cursor = db.conn.cursor()
            cursor.execute('UPDATE users SET email_notify_enabled = ? WHERE username = ?', (1 if enabled else 0, username))
            db.conn.commit()
            if cursor.rowcount == 0:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            return jsonify({'success': True, 'message': '邮件提醒设置已更新', 'data': {'enabled': enabled}})
        except Exception as e:
            logger.error('更新邮件提醒设置失败:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/api/settings-public', methods=['GET'])
    def get_public_settings():
        try:
            keys = ['tickets_enabled', 'feature_blacklist_enabled', 'site_title', 'register_email_verification_enabled', 'allowed_email_suffixes', 'background_image_api', 'pay_per_use_enabled']
            settings = db.get_settings(keys)
            return jsonify({
                'success': True,
                'data': {
                    'ticketsEnabled': _bool_from_setting(settings.get('tickets_enabled', '1')),
                    'featureBlacklistEnabled': _bool_from_setting(settings.get('feature_blacklist_enabled', '1')),
                    'siteTitle': settings.get('site_title') or '',
                    'registerEmailVerificationEnabled': _bool_from_setting(settings.get('register_email_verification_enabled', '0'), default_true=False),
                    'backgroundImageApi': settings.get('background_image_api') or 'https://t.alcy.cc/ycy',
                    'payPerUseEnabled': _bool_from_setting(settings.get('pay_per_use_enabled', '1'))
                }
            })
        except Exception as e:
            logger.error('读取公开设置失败:', e)
            return jsonify({'success': False, 'message': '读取设置失败'}), 500
    
    @app.route('/user/change-password', methods=['POST'])
    def change_password():
        """用户修改密码"""
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        data = request.json
        old_password = data.get('oldPassword', '')
        new_password = data.get('newPassword', '')
        qq_verify = data.get('qqVerify', '').strip()
        
        # 验证输入
        if not all([old_password, new_password, qq_verify]):
            return jsonify({'success': False, 'message': '所有字段都不能为空'}), 400
        
        if len(new_password) < 6:
            return jsonify({'success': False, 'message': '新密码长度必须至少6个字符'}), 400
        
        if old_password == new_password:
            return jsonify({'success': False, 'message': '新密码不能与旧密码相同'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 获取用户信息
            cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
            user = cursor.fetchone()
            
            if not user:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            # 验证旧密码
            if not bcrypt.checkpw(old_password.encode('utf-8'), user['password'].encode('utf-8')):
                return jsonify({'success': False, 'message': '旧密码错误'}), 401
            
            # 验证QQ号
            if user['qq'] != qq_verify:
                return jsonify({'success': False, 'message': 'QQ号验证失败'}), 401
            
            # 生成新密码的哈希
            new_hashed = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt())
            
            # 更新密码
            cursor.execute(
                'UPDATE users SET password = ? WHERE username = ?',
                (new_hashed.decode('utf-8'), username)
            )
            db.conn.commit()
            
            logger.info(f'用户 {username} 成功修改密码')
            
            return jsonify({
                'success': True,
                'message': '密码修改成功，请使用新密码重新登录'
            })
            
        except Exception as e:
            logger.error('修改密码过程中出错:', e)
            return jsonify({'success': False, 'message': f'修改密码失败: {str(e)}'}), 500
    
    @app.route('/bind', methods=['POST'])
    def bind():
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        data = request.json
        activation_code = data.get('orderNumber', '').strip()
        group_number = data.get('groupNumber', '').strip()
        
        if not activation_code or not group_number:
            return jsonify({'success': False, 'message': '激活码和群号均不能为空'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 查询激活码信息
            cursor.execute('SELECT * FROM activation_codes WHERE code = ?', (activation_code,))
            code_info = cursor.fetchone()
            
            if not code_info:
                return jsonify({'success': False, 'message': '无效的激活码'}), 400
            
            if code_info['is_used']:
                return jsonify({'success': False, 'message': '该激活码已被使用'}), 400
            
            sku_id = code_info['sku_id']
            duration_months = code_info['duration_months']
            
            # 检查该群组是否已有相同档位的绑定（不限制用户）
            cursor.execute('''
                SELECT * FROM bindings 
                WHERE group_number = ? AND sku_id = ?
                ORDER BY expiration_time DESC
                LIMIT 1
            ''', (group_number, sku_id))
            existing_binding = cursor.fetchone()
            
            is_renewal = existing_binding is not None
            
            # 计算新的过期时间
            if is_renewal and existing_binding['expiration_time']:
                # 续费：从当前到期时间开始计算
                base_time = datetime.fromisoformat(existing_binding['expiration_time'])
                if base_time < datetime.now():
                    base_time = datetime.now()
            else:
                # 新绑定：从现在开始计算
                base_time = datetime.now()
            
            if duration_months == 0:
                expiration_time = None  # 永久有效
            else:
                # 计算到期时间
                expiration_time = base_time
                for _ in range(duration_months):
                    if expiration_time.month == 12:
                        expiration_time = expiration_time.replace(year=expiration_time.year + 1, month=1)
                    else:
                        expiration_time = expiration_time.replace(month=expiration_time.month + 1)
                expiration_time = expiration_time.isoformat()
            
            # 开始事务
            db.conn.execute('BEGIN IMMEDIATE')
            
            try:
                # 标记激活码为已使用
                cursor.execute('''
                    UPDATE activation_codes 
                    SET is_used = 1, used_by = ?, used_time = ?
                    WHERE code = ?
                ''', (username, datetime.now().isoformat(), activation_code))
                
                if is_renewal and existing_binding:
                    # 如果是续费，更新现有记录的到期时间
                    cursor.execute('''
                        UPDATE bindings 
                        SET expiration_time = ?
                        WHERE group_number = ? AND sku_id = ?
                    ''', (expiration_time, group_number, sku_id))
                    
                    # 同时创建一条新的绑定记录用于记录谁续费的
                    cursor.execute('''
                        INSERT INTO bindings 
                        (activation_code, sku_id, group_number, owner_username, expiration_time)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (activation_code, sku_id, group_number, username, expiration_time))
                else:
                    # 创建新的绑定记录
                    cursor.execute('''
                        INSERT INTO bindings 
                        (activation_code, sku_id, group_number, owner_username, expiration_time)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (activation_code, sku_id, group_number, username, expiration_time))
                
                db.conn.commit()
                
                logger.info(f'用户 {username} {"续费" if is_renewal else "绑定"}群 {group_number} 成功，使用激活码 {activation_code}')
                
                return jsonify({
                    'success': True,
                    'message': '续费成功' if is_renewal else '绑定成功',
                    'data': {
                        'orderNumber': activation_code,
                        'groupNumber': group_number,
                        'skuType': config.get_tier_name(sku_id),
                        'expirationDate': expiration_time if expiration_time else '永久有效',
                        'isRenewal': is_renewal
                    }
                })
                
            except Exception as e:
                db.conn.rollback()
                raise e
                
        except Exception as e:
            logger.error('绑定过程中出错:', e)
            return jsonify({'success': False, 'message': f'绑定失败: {str(e)}'}), 500
    
    @app.route('/user/bindings', methods=['GET'])
    def get_user_bindings():
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        try:
            cursor = db.conn.cursor()
            
            # 查询用户的所有绑定记录，按群号和SKU分组
            cursor.execute('''
                SELECT 
                    b1.group_number,
                    b1.sku_id,
                    MAX(b1.expiration_time) as expiration_time,
                    MIN(b1.bind_time) as first_bind_time,
                    GROUP_CONCAT(b1.activation_code, ',') as activation_codes,
                    COUNT(DISTINCT b1.activation_code) as renewal_count,
                    GROUP_CONCAT(DISTINCT b2.owner_username) as all_contributors
                FROM bindings b1
                LEFT JOIN bindings b2 ON b1.group_number = b2.group_number AND b1.sku_id = b2.sku_id
                WHERE b1.owner_username = ?
                GROUP BY b1.group_number, b1.sku_id
                ORDER BY MAX(b1.expiration_time) DESC
            ''', (username,))
            
            bindings = []
            for row in cursor.fetchall():
                activation_codes = row['activation_codes'].split(',') if row['activation_codes'] else []
                contributors = row['all_contributors'].split(',') if row['all_contributors'] else []
                
                bindings.append({
                    'orderNumber': activation_codes[0] if activation_codes else '',
                    'activationCodes': activation_codes,
                    'groupNumber': row['group_number'],
                    'skuType': config.get_tier_name(row['sku_id']),
                    'skuId': row['sku_id'],
                    'expirationDate': row['expiration_time'] if row['expiration_time'] else '永久有效',
                    'bindTime': row['first_bind_time'],
                    'renewalCount': row['renewal_count'] - 1,
                    'isShared': len(set(contributors)) > 1,  # 是否有其他用户也贡献了续费
                    'contributors': list(set(contributors))
                })
            
            return jsonify({
                'success': True,
                'data': bindings
            })
            
        except Exception as e:
            logger.error('获取用户绑定记录时出错:', e)
            return jsonify({'success': False, 'message': f'获取记录失败: {str(e)}'}), 500

    @app.route('/api/tiers', methods=['GET'])
    def get_tiers():
        """获取档位配置（公开接口）"""
        # 只返回必要的公开信息
        public_tiers = []
        for tier in config.TIERS:
            public_tiers.append({
                'id': tier['id'],
                'sku_id': tier['sku_id'],
                'name': tier['display_name'],
                'price': tier.get('price', 0),
                'description': tier.get('description', ''),
                'features': tier.get('features', [])
            })
        return jsonify({'success': True, 'data': public_tiers})
    
    @app.route('/user/group/<group_number>/blacklist', methods=['GET'])
    def get_group_blacklist(group_number):
        """获取群组的功能黑名单"""
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        try:
            cursor = db.conn.cursor()
            
            # 验证用户是否拥有该群组
            cursor.execute('''
                SELECT sku_id FROM bindings 
                WHERE group_number = ? AND owner_username = ?
                AND (expiration_time IS NULL OR datetime(expiration_time) > datetime('now'))
                LIMIT 1
            ''', (group_number, username))
            
            binding = cursor.fetchone()
            if not binding:
                return jsonify({'success': False, 'message': '您没有权限管理该群组'}), 403
            
            sku_id = binding['sku_id']
            
            # 获取该套餐可用的功能黑名单
            cursor.execute('''
                SELECT f.*, 
                       CASE WHEN gb.id IS NOT NULL THEN 1 ELSE 0 END as is_enabled
                FROM feature_blacklist_definitions f
                LEFT JOIN group_feature_blacklist gb 
                    ON f.id = gb.feature_id AND gb.group_number = ?
                WHERE f.allowed_tiers LIKE '%' || ? || '%'
                ORDER BY f.id DESC
            ''', (group_number, sku_id))
            
            features = []
            for row in cursor.fetchall():
                features.append({
                    'id': row['id'],
                    'displayName': row['display_name'],
                    'actualFeatures': row['actual_features'].split(','),
                    'isEnabled': bool(row['is_enabled'])
                })
            
            return jsonify({'success': True, 'data': features})
        except Exception as e:
            logger.error('获取群组功能黑名单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/user/group/<group_number>/blacklist/toggle', methods=['POST'])
    def toggle_group_blacklist(group_number):
        """切换群组功能黑名单状态"""
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        data = request.json
        feature_id = data.get('featureId')
        enabled = data.get('enabled')
        
        if feature_id is None or enabled is None:
            return jsonify({'success': False, 'message': '参数不完整'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 验证权限
            cursor.execute('''
                SELECT sku_id FROM bindings 
                WHERE group_number = ? AND owner_username = ?
                AND (expiration_time IS NULL OR datetime(expiration_time) > datetime('now'))
                LIMIT 1
            ''', (group_number, username))
            
            binding = cursor.fetchone()
            if not binding:
                return jsonify({'success': False, 'message': '您没有权限管理该群组'}), 403
            
            # 验证功能是否允许该套餐使用
            cursor.execute('''
                SELECT allowed_tiers FROM feature_blacklist_definitions
                WHERE id = ? AND allowed_tiers LIKE '%' || ? || '%'
            ''', (feature_id, binding['sku_id']))
            
            if not cursor.fetchone():
                return jsonify({'success': False, 'message': '该功能不适用于您的套餐'}), 403
            
            if enabled:
                # 添加黑名单
                cursor.execute('''
                    INSERT OR IGNORE INTO group_feature_blacklist (group_number, feature_id)
                    VALUES (?, ?)
                ''', (group_number, feature_id))
            else:
                # 移除黑名单
                cursor.execute('''
                    DELETE FROM group_feature_blacklist 
                    WHERE group_number = ? AND feature_id = ?
                ''', (group_number, feature_id))
            
            db.conn.commit()
            
            return jsonify({
                'success': True,
                'message': '功能黑名单已更新'
            })
        except Exception as e:
            logger.error('切换群组功能黑名单时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    # 用户工单相关路由
    @app.route('/tickets/types', methods=['GET'])
    def get_ticket_types():
        """获取工单类型列表（公开接口）"""
        disabled = _ensure_feature_enabled_or_403('tickets_enabled', '工单')
        if disabled:
            return disabled
        try:
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT * FROM ticket_types 
                WHERE is_active = 1
                ORDER BY sort_order ASC, id ASC
            ''')
            
            types = []
            for row in cursor.fetchall():
                types.append({
                    'id': row['id'],
                    'name': row['name'],
                    'description': row['description']
                })
            
            return jsonify({'success': True, 'data': types})
        except Exception as e:
            logger.error('获取工单类型列表时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/tickets/create', methods=['POST'])
    def create_ticket():
        """创建工单"""
        disabled = _ensure_feature_enabled_or_403('tickets_enabled', '工单')
        if disabled:
            return disabled
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        data = request.json
        title = data.get('title', '').strip()
        content = data.get('content', '').strip()
        ticket_type_id = data.get('typeId')
        priority = data.get('priority', 'normal')
        group_number = data.get('groupNumber', '').strip()
        
        if not all([title, content, ticket_type_id]):
            return jsonify({'success': False, 'message': '标题、内容和类型不能为空'}), 400
        
        if len(title) > 100:
            return jsonify({'success': False, 'message': '标题长度不能超过100个字符'}), 400
        
        if len(content) > 2000:
            return jsonify({'success': False, 'message': '内容长度不能超过2000个字符'}), 400
        
        valid_priorities = ['low', 'normal', 'high']
        if priority not in valid_priorities:
            priority = 'normal'
        
        try:
            cursor = db.conn.cursor()
            
            # 获取用户ID
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            user = cursor.fetchone()
            if not user:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            user_id = user['id']
            
            # 验证工单类型
            cursor.execute('SELECT * FROM ticket_types WHERE id = ? AND is_active = 1', (ticket_type_id,))
            if not cursor.fetchone():
                return jsonify({'success': False, 'message': '无效的工单类型'}), 400
            
            # 生成工单号
            import secrets
            ticket_number = f"T{datetime.now().strftime('%Y%m%d')}{secrets.token_hex(4).upper()}"
            
            # 创建工单
            cursor.execute('''
                INSERT INTO tickets (ticket_number, title, content, ticket_type_id, priority, user_id, group_number)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (ticket_number, title, content, ticket_type_id, priority, user_id, group_number))
            
            ticket_id = cursor.lastrowid
            
            # 记录状态变更
            cursor.execute('''
                INSERT INTO ticket_status_logs (ticket_id, old_status, new_status, changed_by, change_reason)
                VALUES (?, ?, ?, ?, ?)
            ''', (ticket_id, '', 'open', user_id, '用户创建工单'))
            
            # 新工单创建：根据开关决定是否提醒管理员，但要做去重控制（新工单默认未提醒）
            try:
                # 初始化标记为未提醒
                cursor.execute('UPDATE tickets SET admin_notified_flag = 0 WHERE id = ?', (ticket_id,))
            except Exception:
                pass

            db.conn.commit()
            
            logger.info(f'用户 {username} 创建工单成功: {ticket_number}')
            
            # 创建后进行管理员提醒（若开启且尚未提醒）
            try:
                admin_notify_enabled = str(db.get_setting('admin_ticket_email_notify_enabled', '0')) in ('1','true','True','yes','on')
                if admin_notify_enabled:
                    cursor = db.conn.cursor()
                    cursor.execute('SELECT admin_notified_flag FROM tickets WHERE id = ?', (ticket_id,))
                    row_flag = cursor.fetchone()
                    already = bool(row_flag and row_flag['admin_notified_flag'])
                    if not already:
                        admin_email = db.get_setting('admin_email', '') or ''
                        if admin_email:
                            site_name = db.get_setting('site_title', '') or ''
                            subject = f"{site_name} 有新工单" if site_name else '有新工单'
                            view_link = db.get_setting('site_link', '') or ''
                            # 复用工单模板
                            content_preview = f"用户提交的工单：{title}"
                            html = (
                                render_work_order_email_html(subject, content_preview, ticket_number=ticket_number, created_time=datetime.now().isoformat(), view_link=view_link, site_name_override=site_name)
                                if 'render_work_order_email_html' in globals() else
                                f"<!doctype html><html><body><h3>{subject}</h3><div>{content_preview}</div><div>工单号：{ticket_number}</div></body></html>"
                            )
                            try:
                                app.enqueue_email(admin_email, subject, html, {'actor': 'auto', 'action': f'新工单提醒 工单{ticket_number}'})
                            except Exception:
                                try:
                                    send_email_smtp(admin_email, subject, html)
                                except Exception:
                                    pass
                            # 标记为已提醒
                            try:
                                cursor.execute('UPDATE tickets SET admin_notified_flag = 1 WHERE id = ?', (ticket_id,))
                                db.conn.commit()
                            except Exception:
                                pass
            except Exception:
                pass

            return jsonify({
                'success': True,
                'message': '工单创建成功',
                'data': {
                    'ticketId': ticket_id,
                    'ticketNumber': ticket_number
                }
            })
            
        except Exception as e:
            logger.error('创建工单时出错:', e)
            return jsonify({'success': False, 'message': f'创建失败: {str(e)}'}), 500

    @app.route('/tickets/my', methods=['GET'])
    def get_my_tickets():
        """获取我的工单列表"""
        disabled = _ensure_feature_enabled_or_403('tickets_enabled', '工单')
        if disabled:
            return disabled
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        try:
            cursor = db.conn.cursor()
            
            # 获取用户ID
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            user = cursor.fetchone()
            if not user:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            user_id = user['id']
            
            # 获取工单列表
            cursor.execute('''
                SELECT 
                    t.*,
                    tt.name as type_name,
                    COUNT(r.id) as reply_count
                FROM tickets t
                LEFT JOIN ticket_types tt ON t.ticket_type_id = tt.id
                LEFT JOIN ticket_replies r ON t.id = r.ticket_id
                WHERE t.user_id = ?
                GROUP BY t.id
                ORDER BY t.created_time DESC
            ''', (user_id,))
            
            tickets = []
            for row in cursor.fetchall():
                tickets.append({
                    'id': row['id'],
                    'ticketNumber': row['ticket_number'],
                    'title': row['title'],
                    'typeName': row['type_name'],
                    'status': row['status'],
                    'priority': row['priority'],
                    'groupNumber': row['group_number'],
                    'createdTime': row['created_time'],
                    'updatedTime': row['updated_time'],
                    'replyCount': row['reply_count']
                })
            
            return jsonify({
                'success': True,
                'data': tickets
            })
            
        except Exception as e:
            logger.error('获取我的工单列表时出错:', e)
            return jsonify({'success': False, 'message': f'获取失败: {str(e)}'}), 500

    @app.route('/tickets/<int:ticket_id>', methods=['GET'])
    def get_ticket_detail(ticket_id):
        """获取工单详情"""
        disabled = _ensure_feature_enabled_or_403('tickets_enabled', '工单')
        if disabled:
            return disabled
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        try:
            cursor = db.conn.cursor()
            
            # 获取用户ID
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            user = cursor.fetchone()
            if not user:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            user_id = user['id']
            
            # 获取工单详情
            cursor.execute('''
                SELECT 
                    t.*,
                    tt.name as type_name
                FROM tickets t
                LEFT JOIN ticket_types tt ON t.ticket_type_id = tt.id
                WHERE t.id = ? AND t.user_id = ?
            ''', (ticket_id, user_id))
            
            ticket = cursor.fetchone()
            if not ticket:
                return jsonify({'success': False, 'message': '工单不存在或无权限访问'}), 404
            
            # 获取工单回复
            cursor.execute('''
                SELECT 
                    r.*,
                    u.username,
                    u.qq
                FROM ticket_replies r
                LEFT JOIN users u ON r.user_id = u.id
                WHERE r.ticket_id = ?
                ORDER BY r.created_time ASC
            ''', (ticket_id,))
            
            replies = []
            for reply in cursor.fetchall():
                replies.append({
                    'id': reply['id'],
                    'content': reply['content'],
                    'isAdmin': bool(reply['is_admin']),
                    'username': reply['username'],
                    'qq': reply['qq'],
                    'createdTime': reply['created_time']
                })
            
            # 获取工单附件
            cursor.execute('''
                SELECT * FROM ticket_attachments 
                WHERE ticket_id = ? OR reply_id IN (
                    SELECT id FROM ticket_replies WHERE ticket_id = ?
                )
                ORDER BY upload_time ASC
            ''', (ticket_id, ticket_id))
            
            attachments = []
            for attachment in cursor.fetchall():
                attachments.append({
                    'id': attachment['id'],
                    'fileName': attachment['file_name'],
                    'filePath': attachment['file_path'],
                    'fileSize': attachment['file_size'],
                    'fileType': attachment['file_type'],
                    'uploadTime': attachment['upload_time'],
                    'isReplyAttachment': attachment['reply_id'] is not None
                })
            
            ticket_data = {
                'id': ticket['id'],
                'ticketNumber': ticket['ticket_number'],
                'title': ticket['title'],
                'content': ticket['content'],
                'typeName': ticket['type_name'],
                'status': ticket['status'],
                'priority': ticket['priority'],
                'groupNumber': ticket['group_number'],
                'createdTime': ticket['created_time'],
                'updatedTime': ticket['updated_time'],
                'replies': replies,
                'attachments': attachments
            }
            
            return jsonify({'success': True, 'data': ticket_data})
            
        except Exception as e:
            logger.error('获取工单详情时出错:', e)
            return jsonify({'success': False, 'message': f'获取失败: {str(e)}'}), 500

    # 新增：获取工单回复列表（供前端“去回复”页面使用）
    # 兼容旧前端可能请求 /tickets/<id>/replyList 的情况
    @app.route('/tickets/<int:ticket_id>/replies', methods=['GET'])
    @app.route('/tickets/<int:ticket_id>/replyList', methods=['GET'])
    def get_ticket_replies(ticket_id: int):
        disabled = _ensure_feature_enabled_or_403('tickets_enabled', '工单')
        if disabled:
            return disabled
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401

        try:
            cursor = db.conn.cursor()
            # 校验归属
            cursor.execute('''
                SELECT t.id FROM tickets t
                JOIN users u ON t.user_id = u.id
                WHERE t.id = ? AND u.username = ?
            ''', (ticket_id, username))
            if not cursor.fetchone():
                return jsonify({'success': False, 'message': '工单不存在或无权限访问'}), 404

            cursor.execute('''
                SELECT r.*, u.username, u.qq
                FROM ticket_replies r
                LEFT JOIN users u ON r.user_id = u.id
                WHERE r.ticket_id = ?
                ORDER BY r.created_time ASC
            ''', (ticket_id,))
            replies = []
            for row in cursor.fetchall():
                replies.append({
                    'id': row['id'],
                    'content': row['content'],
                    'isAdmin': bool(row['is_admin']),
                    'username': row['username'],
                    'qq': row['qq'],
                    'createdTime': row['created_time']
                })

            return jsonify({'success': True, 'data': replies})
        except Exception as e:
            logger.error('获取工单回复列表时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/tickets/<int:ticket_id>/attachments', methods=['POST'])
    def upload_ticket_attachment(ticket_id: int):
        """用户上传工单附件（绑定到工单本身）"""
        disabled = _ensure_feature_enabled_or_403('tickets_enabled', '工单')
        if disabled:
            return disabled
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401

        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401

        try:
            # 校验归属
            cursor = db.conn.cursor()
            cursor.execute('''
                SELECT t.id FROM tickets t
                JOIN users u ON t.user_id = u.id
                WHERE t.id = ? AND u.username = ?
            ''', (ticket_id, username))
            ticket = cursor.fetchone()
            if not ticket:
                return jsonify({'success': False, 'message': '工单不存在或无权限上传'}), 403

            if 'file' not in request.files:
                return jsonify({'success': False, 'message': '未选择文件'}), 400

            file = request.files['file']
            if file.filename == '':
                return jsonify({'success': False, 'message': '未选择文件'}), 400

            if not _allowed_file(file.filename):
                return jsonify({'success': False, 'message': '不支持的文件类型'}), 400

            # 文件大小
            file.stream.seek(0, os.SEEK_END)
            size = file.stream.tell()
            file.stream.seek(0)
            if size > MAX_FILE_SIZE:
                return jsonify({'success': False, 'message': '文件大小超过限制（10MB）'}), 400

            # 生成保存文件名
            import uuid
            ext = file.filename.rsplit('.', 1)[1].lower()
            safe_filename = f"{uuid.uuid4().hex}.{ext}"
            save_path = os.path.join(UPLOAD_FOLDER, safe_filename)
            file.save(save_path)

            # 记录数据库
            cursor.execute('''
                INSERT INTO ticket_attachments (ticket_id, reply_id, file_name, file_path, file_size, file_type)
                VALUES (?, NULL, ?, ?, ?, ?)
            ''', (ticket_id, file.filename, safe_filename, size, ext))
            db.conn.commit()

            return jsonify({
                'success': True,
                'message': '附件上传成功',
                'data': {
                    'fileName': file.filename,
                    'filePath': safe_filename,
                    'fileSize': size,
                    'fileType': ext
                }
            })
        except Exception as e:
            logger.error('用户上传工单附件时出错:', e)
            return jsonify({'success': False, 'message': f'上传失败: {str(e)}'}), 500

    @app.route('/files/<path:filename>', methods=['GET'])
    def get_user_file(filename: str):
        """用户访问自己工单的附件文件。
        做鉴权与归属校验，防止越权下载。
        """
        auth_header = request.headers.get('Authorization')
        token = None
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
        # 兼容图片/文件标签无法带 Authorization 头的情况，从查询参数读取 token
        if token is None:
            token = request.args.get('token')
        if not token:
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401

        try:
            cursor = db.conn.cursor()
            # 校验该文件是否属于当前用户的某个工单附件
            cursor.execute('''
                SELECT ta.id FROM ticket_attachments ta
                JOIN tickets t ON ta.ticket_id = t.id
                JOIN users u ON t.user_id = u.id
                WHERE ta.file_path = ? AND u.username = ?
                LIMIT 1
            ''', (filename, username))
            if not cursor.fetchone():
                return jsonify({'success': False, 'message': '文件不存在或无权限访问'}), 404

            file_path = os.path.join(UPLOAD_FOLDER, filename)
            if not os.path.exists(file_path):
                return jsonify({'success': False, 'message': '文件不存在'}), 404

            return send_file(file_path)
        except Exception as e:
            logger.error('用户访问附件文件时出错:', e)
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/tickets/<int:ticket_id>/reply', methods=['POST'])
    def reply_ticket(ticket_id):
        """用户回复工单"""
        disabled = _ensure_feature_enabled_or_403('tickets_enabled', '工单')
        if disabled:
            return disabled
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        data = request.json
        content = data.get('content', '').strip()
        
        if not content:
            return jsonify({'success': False, 'message': '回复内容不能为空'}), 400
        
        if len(content) > 2000:
            return jsonify({'success': False, 'message': '回复内容不能超过2000个字符'}), 400
        
        try:
            cursor = db.conn.cursor()
            
            # 获取用户ID
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            user = cursor.fetchone()
            if not user:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            user_id = user['id']
            
            # 检查工单是否存在且属于该用户
            cursor.execute('SELECT status FROM tickets WHERE id = ? AND user_id = ?', (ticket_id, user_id))
            ticket = cursor.fetchone()
            if not ticket:
                return jsonify({'success': False, 'message': '工单不存在或无权限访问'}), 404
            
            if ticket['status'] == 'closed':
                return jsonify({'success': False, 'message': '工单已关闭，无法回复'}), 400
            
            # 添加回复
            cursor.execute('''
                INSERT INTO ticket_replies (ticket_id, user_id, content, is_admin)
                VALUES (?, ?, ?, 0)
            ''', (ticket_id, user_id, content))
            
            # 更新工单状态为"等待回复"
            if ticket['status'] == 'in_progress':
                cursor.execute('''
                    UPDATE tickets SET status = 'waiting', updated_time = ?
                    WHERE id = ?
                ''', (datetime.now().isoformat(), ticket_id))
                
                # 记录状态变更
                cursor.execute('''
                    INSERT INTO ticket_status_logs (ticket_id, old_status, new_status, changed_by, change_reason)
                    VALUES (?, ?, ?, ?, ?)
                ''', (ticket_id, 'in_progress', 'waiting', user_id, '用户回复'))

            # 用户回复导致等待管理员：按开关且仅一次提醒管理员（若管理员未回复）
            try:
                admin_notify_enabled = str(db.get_setting('admin_ticket_email_notify_enabled', '0')) in ('1','true','True','yes','on')
                if admin_notify_enabled:
                    # 仅当当前标记未提醒过时发送
                    cursor.execute('SELECT admin_notified_flag, ticket_number, created_time FROM tickets WHERE id = ?', (ticket_id,))
                    trow = cursor.fetchone()
                    should_send = trow and (trow['admin_notified_flag'] is None or int(trow['admin_notified_flag']) == 0)
                    if should_send:
                        admin_email = db.get_setting('admin_email', '') or ''
                        if admin_email:
                            site_name = db.get_setting('site_title', '') or ''
                            subject = f"{site_name} 工单等待管理员回复" if site_name else '工单等待管理员回复'
                            view_link = db.get_setting('site_link', '') or ''
                            content_preview = content
                            html = (
                                render_work_order_email_html(subject, content_preview, ticket_number=trow['ticket_number'], created_time=(trow['created_time'] or ''), view_link=view_link, site_name_override=site_name)
                                if 'render_work_order_email_html' in globals() else
                                f"<!doctype html><html><body><h3>{subject}</h3><div>{content_preview}</div><div>工单号：{trow['ticket_number']}</div></body></html>"
                            )
                            try:
                                app.enqueue_email(admin_email, subject, html, {'actor': 'auto', 'action': f'工单等待提醒 工单{trow["ticket_number"]}'})
                            except Exception:
                                try:
                                    send_email_smtp(admin_email, subject, html)
                                except Exception:
                                    pass
                            # 标记管理员已提醒
                            try:
                                cursor.execute('UPDATE tickets SET admin_notified_flag = 1 WHERE id = ?', (ticket_id,))
                            except Exception:
                                pass
            except Exception:
                pass

            # 用户回复后，重置“用户已通知”标记，便于后续管理员再次回复仍可通知（在非每条提醒模式下）
            try:
                cursor.execute('UPDATE tickets SET user_notified_flag = 0 WHERE id = ?', (ticket_id,))
            except Exception:
                pass

            db.conn.commit()
            
            return jsonify({
                'success': True,
                'message': '回复成功'
            })
            
        except Exception as e:
            logger.error('用户回复工单时出错:', e)
            return jsonify({'success': False, 'message': f'回复失败: {str(e)}'}), 500

    @app.route('/tickets/<int:ticket_id>/close', methods=['POST'])
    def close_ticket(ticket_id):
        """用户关闭工单"""
        disabled = _ensure_feature_enabled_or_403('tickets_enabled', '工单')
        if disabled:
            return disabled
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证信息'}), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            username = payload['username']
        except jwt.ExpiredSignatureError:
            return jsonify({'success': False, 'message': '登录已过期，请重新登录'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'success': False, 'message': '无效的认证信息'}), 401
        
        try:
            cursor = db.conn.cursor()
            
            # 获取用户ID
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            user = cursor.fetchone()
            if not user:
                return jsonify({'success': False, 'message': '用户不存在'}), 404
            
            user_id = user['id']
            
            # 检查工单是否存在且属于该用户
            cursor.execute('SELECT status FROM tickets WHERE id = ? AND user_id = ?', (ticket_id, user_id))
            ticket = cursor.fetchone()
            if not ticket:
                return jsonify({'success': False, 'message': '工单不存在或无权限访问'}), 404
            
            if ticket['status'] == 'closed':
                return jsonify({'success': False, 'message': '工单已经关闭'}), 400
            
            # 关闭工单
            cursor.execute('''
                UPDATE tickets SET status = 'closed', closed_time = ?, closed_by = ?, updated_time = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), user_id, datetime.now().isoformat(), ticket_id))
            
            # 记录状态变更
            cursor.execute('''
                INSERT INTO ticket_status_logs (ticket_id, old_status, new_status, changed_by, change_reason)
                VALUES (?, ?, ?, ?, ?)
            ''', (ticket_id, ticket['status'], 'closed', user_id, '用户关闭工单'))
            
            db.conn.commit()
            
            return jsonify({
                'success': True,
                'message': '工单已关闭'
            })
            
        except Exception as e:
            logger.error('用户关闭工单时出错:', e)
            return jsonify({'success': False, 'message': f'关闭失败: {str(e)}'}), 500

    # ==================== 按次付费功能用户查询接口 ====================

    @app.route('/user/pay-per-use/balance', methods=['GET'])
    def user_get_pay_per_use_balance():
        """用户查询自己的按次付费功能余额"""
        try:
            # 获取用户身份验证信息
            auth_header = request.headers.get('Authorization', '')
            if not auth_header.startswith('Bearer '):
                return jsonify({'success': False, 'message': '未授权访问'}), 401

            token = auth_header[7:]  # 移除 'Bearer ' 前缀
            try:
                import jwt
                payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
                username = payload.get('username')
                if not username:
                    return jsonify({'success': False, 'message': '无效的身份验证令牌'}), 401
            except jwt.ExpiredSignatureError:
                return jsonify({'success': False, 'message': '身份验证令牌已过期'}), 401
            except jwt.InvalidTokenError:
                return jsonify({'success': False, 'message': '无效的身份验证令牌'}), 401

            cursor = db.conn.cursor()

            # 获取用户的所有绑定群聊
            cursor.execute('''
                SELECT DISTINCT b.group_number
                FROM bindings b
                WHERE b.owner_username = ? AND (b.expiration_time IS NULL OR datetime(b.expiration_time) > datetime('now'))
            ''', (username,))

            user_groups = cursor.fetchall()
            if not user_groups:
                return jsonify({
                    'success': True,
                    'message': '您没有激活的群聊',
                    'data': []
                })

            group_numbers = [group['group_number'] for group in user_groups]

            # 获取这些群聊的按次付费账户信息
            placeholders = ','.join('?' for _ in group_numbers)
            cursor.execute(f'''
                SELECT ppb.*, ppf.display_name as feature_name, ppf.description as feature_description,
                       ppt.display_name as tier_name, ppt.price_per_use, b.owner_username
                FROM pay_per_use_billing ppb
                LEFT JOIN pay_per_use_features ppf ON ppb.feature_id = ppf.id
                LEFT JOIN pay_per_use_tiers ppt ON ppb.tier_id = ppt.id
                LEFT JOIN bindings b ON ppb.group_number = b.group_number
                WHERE ppb.group_number IN ({placeholders})
                ORDER BY ppb.group_number, ppf.display_name, ppt.price_per_use
            ''', group_numbers)

            accounts = cursor.fetchall()

            # 按群聊分组整理数据
            group_balance = {}
            for account in accounts:
                group_num = account['group_number']
                if group_num not in group_balance:
                    group_balance[group_num] = {
                        'group_number': group_num,
                        'owner_username': account['owner_username'],
                        'features': []
                    }

                group_balance[group_num]['features'].append({
                    'feature_name': account['feature_name'],
                    'feature_description': account['feature_description'],
                    'tier_name': account['tier_name'],
                    'remaining_count': account['remaining_count'],
                    'total_purchased': account['total_purchased'],
                    'price_per_use': account['price_per_use']
                })

            return jsonify({
                'success': True,
                'data': list(group_balance.values())
            })

        except Exception as e:
            logger.error(f'用户查询按次付费功能余额时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/user/pay-per-use/logs', methods=['GET'])
    def user_get_pay_per_use_logs():
        """用户查询按次付费功能使用记录"""
        try:
            # 获取用户身份验证信息
            auth_header = request.headers.get('Authorization', '')
            if not auth_header.startswith('Bearer '):
                return jsonify({'success': False, 'message': '未授权访问'}), 401

            token = auth_header[7:]  # 移除 'Bearer ' 前缀
            try:
                import jwt
                payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
                username = payload.get('username')
                if not username:
                    return jsonify({'success': False, 'message': '无效的身份验证令牌'}), 401
            except jwt.ExpiredSignatureError:
                return jsonify({'success': False, 'message': '身份验证令牌已过期'}), 401
            except jwt.InvalidTokenError:
                return jsonify({'success': False, 'message': '无效的身份验证令牌'}), 401

            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))
            offset = (page - 1) * per_page
            group_filter = request.args.get('group_number', '').strip()
            date_from = request.args.get('date_from', '').strip()
            date_to = request.args.get('date_to', '').strip()

            cursor = db.conn.cursor()

            # 首先获取用户拥有的所有群组
            cursor.execute('''
                SELECT DISTINCT group_number FROM bindings 
                WHERE owner_username = ? AND (expiration_time IS NULL OR datetime(expiration_time) > datetime('now'))
            ''', (username,))
            user_groups = [row['group_number'] for row in cursor.fetchall()]
            
            if not user_groups:
                # 用户没有任何激活的群组
                return jsonify({
                    'success': True,
                    'data': [],
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': 0,
                        'pages': 0
                    }
                })

            # 构建查询条件
            placeholders = ','.join('?' for _ in user_groups)
            where_clause = f"ppl.group_number IN ({placeholders})"
            params = user_groups.copy()

            if group_filter:
                where_clause += " AND ppl.group_number LIKE ?"
                params.append(f'%{group_filter}%')

            if date_from:
                where_clause += " AND ppl.request_time >= ?"
                params.append(date_from)

            if date_to:
                where_clause += " AND ppl.request_time <= ?"
                params.append(date_to + ' 23:59:59')

            # 获取总数
            cursor.execute(f'''
                SELECT COUNT(*) as total
                FROM pay_per_use_logs ppl
                WHERE {where_clause}
            ''', params)
            total = cursor.fetchone()['total']

            # 获取分页数据
            cursor.execute(f'''
                SELECT ppl.*, ppf.display_name as feature_name
                FROM pay_per_use_logs ppl
                LEFT JOIN pay_per_use_features ppf ON ppl.feature_id = ppf.id
                WHERE {where_clause}
                ORDER BY ppl.request_time DESC
                LIMIT {per_page} OFFSET {offset}
            ''', params)

            logs = cursor.fetchall()

            return jsonify({
                'success': True,
                'data': [dict(row) for row in logs],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            })

        except Exception as e:
            logger.error(f'用户查询按次付费功能使用记录时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/user/pay-per-use/redeem', methods=['POST'])
    def user_redeem_pay_per_use_code():
        """用户使用按次付费兑换码"""
        try:
            # 获取用户身份验证信息
            auth_header = request.headers.get('Authorization', '')
            if not auth_header.startswith('Bearer '):
                return jsonify({'success': False, 'message': '未授权访问'}), 401

            token = auth_header[7:]  # 移除 'Bearer ' 前缀
            try:
                import jwt
                payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
                username = payload.get('username')
                if not username:
                    return jsonify({'success': False, 'message': '无效的身份验证令牌'}), 401
            except jwt.ExpiredSignatureError:
                return jsonify({'success': False, 'message': '身份验证令牌已过期'}), 401
            except jwt.InvalidTokenError:
                return jsonify({'success': False, 'message': '无效的身份验证令牌'}), 401

            data = request.get_json() or {}
            code = data.get('code', '').strip()
            group_number = data.get('group_number', '').strip()

            if not code:
                return jsonify({'success': False, 'message': '兑换码不能为空'}), 400

            if not group_number:
                return jsonify({'success': False, 'message': '群号不能为空'}), 400

            cursor = db.conn.cursor()

            # 验证用户是否拥有该群聊的绑定权限
            cursor.execute('''
                SELECT sku_id FROM bindings
                WHERE group_number = ? AND owner_username = ?
                AND (expiration_time IS NULL OR datetime(expiration_time) > datetime('now'))
                LIMIT 1
            ''', (group_number, username))

            binding = cursor.fetchone()
            if not binding:
                return jsonify({'success': False, 'message': '您没有权限为该群聊使用兑换码，请先绑定群聊'}), 403

            # 验证兑换码是否存在且未使用
            cursor.execute('''
                SELECT * FROM pay_per_use_codes
                WHERE code = ? AND (expires_at IS NULL OR expires_at = '' OR datetime(expires_at) > datetime('now'))
            ''', (code,))

            code_info = cursor.fetchone()
            if not code_info:
                logger.info(f'兑换码验证失败: 用户 {username} 尝试使用不存在的兑换码 {code}')
                return jsonify({'success': False, 'message': '兑换码不存在或已过期'}), 400

            if code_info['is_used']:
                return jsonify({'success': False, 'message': '该兑换码已被使用'}), 400

            # 解析兑换码配置
            import json
            try:
                config = json.loads(code_info['feature_config'])
            except:
                return jsonify({'success': False, 'message': '兑换码配置无效'}), 400

            # 开始事务
            db.conn.execute('BEGIN IMMEDIATE')

            try:
                # 标记兑换码为已使用
                cursor.execute('''
                    UPDATE pay_per_use_codes
                    SET is_used = 1, used_by_username = ?, used_by_group = ?, used_time = ?
                    WHERE code = ?
                ''', (username, group_number, datetime.now().isoformat(), code))

                # 处理每个功能的兑换
                # 配置格式: {'feature_id': usage_count, ...}
                redeemed_features = []
                for feature_id_str, usage_count in config.items():
                    feature_id = int(feature_id_str)
                    usage_count = int(usage_count)
                    
                    if not feature_id or usage_count <= 0:
                        continue

                    # 使用默认tier_id = 1
                    tier_id = 1
                    
                    # 检查是否已有该功能的计费账户
                    cursor.execute('''
                        SELECT * FROM pay_per_use_billing
                        WHERE group_number = ? AND feature_id = ? AND tier_id = ?
                    ''', (group_number, feature_id, tier_id))

                    account = cursor.fetchone()

                    if account:
                        # 更新现有账户
                        new_remaining = account['remaining_count'] + usage_count
                        new_total = account['total_purchased'] + usage_count
                        cursor.execute('''
                            UPDATE pay_per_use_billing
                            SET remaining_count = ?, total_purchased = ?, updated_time = ?
                            WHERE id = ?
                        ''', (new_remaining, new_total, datetime.now().isoformat(), account['id']))
                    else:
                        # 创建新账户
                        cursor.execute('''
                            INSERT INTO pay_per_use_billing (group_number, feature_id, tier_id, remaining_count, total_purchased)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (group_number, feature_id, tier_id, usage_count, usage_count))

                    # 获取功能信息用于返回
                    cursor.execute('''
                        SELECT display_name as feature_name
                        FROM pay_per_use_features
                        WHERE id = ?
                    ''', (feature_id,))

                    feature_info = cursor.fetchone()
                    if feature_info:
                        redeemed_features.append({
                            'feature_name': feature_info['feature_name'],
                            'usage_count': usage_count
                        })

                db.conn.commit()

                logger.info(f'用户 {username} 成功兑换按次付费兑换码 {code}，群号 {group_number}')

                return jsonify({
                    'success': True,
                    'message': '兑换码使用成功',
                    'data': {
                        'code': code,
                        'group_number': group_number,
                        'redeemed_features': redeemed_features
                    }
                })

            except Exception as e:
                db.conn.rollback()
                raise e

        except Exception as e:
            logger.error(f'用户兑换按次付费兑换码时出错: {e}')
            return jsonify({'success': False, 'message': str(e)}), 500